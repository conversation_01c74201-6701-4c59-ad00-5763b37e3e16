<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512"><defs><linearGradient id="a" x1="99.5" x2="232.6" y1="30.7" y2="261.4" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#f3f7fe"/><stop offset=".5" stop-color="#f3f7fe"/><stop offset="1" stop-color="#deeafb"/></linearGradient><symbol id="b" viewBox="0 0 350 222"><path fill="url(#a)" stroke="#e6effc" stroke-miterlimit="10" stroke-width="6" d="m291 107-2.5.1A83.9 83.9 0 00135.6 43 56 56 0 0051 91a56.6 56.6 0 00.8 9A60 60 0 0063 219l4-.2v.2h224a56 56 0 000-112Z"/></symbol></defs><use xlink:href="#b" width="350" height="222" transform="translate(81 145)"/><path fill="#374151" d="M372.5 247a4.6 4.6 0 01-3.2-1.3L350 227.3l-19.3 18.4a4.6 4.6 0 01-6.4 0 4.1 4.1 0 010-6l22.5-21.4a4.6 4.6 0 016.4 0l22.5 21.4a4.1 4.1 0 010 6 4.6 4.6 0 01-3.2 1.3Z" opacity="0"><animateTransform attributeName="transform" begin="-1.2s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 30; 0 0; 0 -30"/><animate attributeName="opacity" begin="-1.2s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path><path fill="#374151" d="M372.5 274a4.6 4.6 0 01-3.2-1.3L350 254.3l-19.3 18.4a4.6 4.6 0 01-6.4 0 4.1 4.1 0 010-6l22.5-21.4a4.6 4.6 0 016.4 0l22.5 21.4a4.1 4.1 0 010 6 4.6 4.6 0 01-3.2 1.3Z" opacity="0"><animateTransform attributeName="transform" begin="-1.1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 30; 0 0; 0 -30"/><animate attributeName="opacity" begin="-1.1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path><path fill="#374151" d="M372.5 301a4.6 4.6 0 01-3.2-1.3L350 281.3l-19.3 18.4a4.6 4.6 0 01-6.4 0 4.1 4.1 0 010-6l22.5-21.4a4.6 4.6 0 016.4 0l22.5 21.4a4.1 4.1 0 010 6 4.6 4.6 0 01-3.2 1.3Z" opacity="0"><animateTransform attributeName="transform" begin="-1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 30; 0 0; 0 -30"/><animate attributeName="opacity" begin="-1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path></svg>