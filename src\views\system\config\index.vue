<script setup lang="tsx">
import { onMounted, ref, watch } from 'vue';
import { NButton, NPopconfirm } from 'naive-ui';
import { useDraggable } from 'vue-draggable-plus';
import { configApi } from '@/service/api/system';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import SvgIcon from '@/components/custom/svg-icon.vue';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SiderBar from './modules/SiderBar.vue';

const appStore = useAppStore();

const current = ref<number>(0);

const configs = ref<Api.System.Config[]>([]);

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination } = useTable<NaiveUI.TableApiFn>({
  apiFn: configApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 1000,
    _sort: 'id',
    _order: 'asc'
  },
  transformer: res => {
    const { records = [], total = 0 } = res.data || {};
    configs.value = records;
    return {
      data: records[current.value]?.params || [],
      pageNum: 1,
      pageSize: 1000,
      total
    };
  },
  columns: () => [
    {
      key: 'drag',
      title: '',
      align: 'right',
      width: 48,
      render: () => <SvgIcon icon="ph:dots-six-vertical" class="inline text-icon" />
    },
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '名称',
      align: 'left'
    },
    {
      key: 'code',
      title: '编码',
      align: 'left'
    },
    {
      key: 'value',
      title: '值',
      align: 'left',
      ellipsis: true
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => handleStatus({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

// 监听SiderBar的选中值，更新表格数据
watch(current, () => {
  data.value = configs.value[current.value].params || [];
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await configApi.save({
    ...configs.value[current.value],
    params: configs.value[current.value].params?.filter(param => !checkedRowKeys.value.includes(param.id))
  });

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: string) {
  const { error } = await configApi.save({
    ...configs.value[current.value],
    params: configs.value[current.value].params?.filter(param => param.id !== id)
  });

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

function edit(id: number) {
  handleEdit(id);
}

async function handleStatus(row: Api.System.ConfigParam) {
  const params = configs.value[current.value].params?.map(param => {
    return param.id === row.id ? row : param;
  });
  // request
  return await configApi.save({ ...configs.value[current.value], params });
}

// 表格引用
const tableRef = ref<any>(null);

// 初始化拖拽
const initDraggable = () => {
  useDraggable(tableRef.value?.$el.querySelector('.n-data-table-tbody'), data, {
    handle: '.n-data-table-td[data-col-key="drag"]',
    animation: 150,
    onEnd: async () => {
      const { error } = await configApi.save({ ...configs.value[current.value], params: data.value });
      if (!error) {
        window.$message?.success('排序更新成功');
      } else {
        window.$message?.error(error.message);
      }
    }
  });
};

onMounted(() => {
  // 等待表格渲染完成
  setTimeout(initDraggable, 300);
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <NLayout has-sider class="sm:flex-1-hidden card-wrapper">
      <NLayoutSider :collapsed-width="0" :width="300" show-trigger="arrow-circle" bordered>
        <SiderBar v-model:value="current" :configs="configs" @search="getDataByPage" />
      </NLayoutSider>
      <NLayoutContent class="h-full">
        <NCard :bordered="false" size="small" class="h-full">
          <template #header>
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </template>
          <NDataTable
            ref="tableRef"
            v-model:checked-row-keys="checkedRowKeys"
            :columns="(columns as any)"
            :data="data"
            size="small"
            :flex-height="!appStore.isMobile"
            :scroll-x="962"
            :loading="loading"
            remote
            striped
            :bordered="false"
            :row-key="row => row.id"
            class="b-t-1px sm:h-full b-auto"
          />
          <OperateDrawer
            v-model:visible="drawerVisible"
            :operate-type="operateType"
            :config="configs[current]"
            :row-data="editingData"
            @submitted="getDataByPage"
          />
        </NCard>
      </NLayoutContent>
    </NLayout>
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-color-picker) {
  .n-color-picker-trigger__value {
    display: none;
  }
}
</style>
