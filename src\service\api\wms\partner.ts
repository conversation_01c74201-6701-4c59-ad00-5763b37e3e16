import { request } from '@/service/request';

export const partnerApi = {
  // 获取合作伙伴列表
  list: (params: Api.Wms.PartnerSearchParams) =>
    request<Api.Wms.PartnerList>({
      url: '/wms/partners',
      params
    }),

  // 创建合作伙伴
  add: (data: Api.Wms.PartnerCreateParams) =>
    request<null>({
      url: '/wms/partners',
      method: 'POST',
      data
    }),

  // 更新合作伙伴
  save: (data: Api.Wms.PartnerUpdateParams) =>
    request<null>({
      url: `/wms/partners/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除合作伙伴
  del: (id: number) =>
    request<null>({
      url: `/wms/partners/${id}`,
      method: 'DELETE'
    }),

  // 批量删除合作伙伴
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/partners',
      method: 'DELETE',
      data: { ids }
    })
};
