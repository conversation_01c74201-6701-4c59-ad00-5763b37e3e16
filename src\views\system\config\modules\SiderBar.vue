<script setup lang="ts">
import { computed, ref } from 'vue';
import { dictApi } from '@/service/api/system';
import { $t } from '@/locales';
import ConfigModal from './ConfigModal.vue';

const props = defineProps<{
  configs: Api.System.Config[];
}>();

interface Emits {
  (e: 'search'): void;
}
const emit = defineEmits<Emits>();

const current = defineModel<number | null | undefined>('value', { required: true });

const operateOptions = [
  {
    label: $t('common.edit'),
    key: 'edit'
  },
  {
    label: $t('common.delete'),
    key: 'delete',
    props: {
      style: {
        color: 'rgb(var(--error-color))'
      }
    }
  }
];

// 根据配置过滤选项
function filterByDict(index: number) {
  current.value = index;
}

const operateType = ref<NaiveUI.TableOperateType>('add');
const modalVisible = ref(false);
const editingData = ref<Api.System.Config | null>(null);

function handleAdd() {
  operateType.value = 'add';
  modalVisible.value = true;
}

function handleEdit(config: Api.System.Config) {
  operateType.value = 'edit';
  modalVisible.value = true;
  editingData.value = config;
}

async function handleDelete(id: number) {
  window.$dialog?.create({
    title: $t('common.warning'),
    content: $t('common.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      const { error } = await dictApi.del(id);
      if (!error) {
        emit('search');
        window.$message?.success($t('common.deleteSuccess'));
      } else {
        window.$message?.error(error.message);
      }
    }
  });
}

function handleOperate(key: string, config: Api.System.Config) {
  if (key === 'edit') {
    handleEdit(config);
  } else if (key === 'delete') {
    handleDelete(config.id);
  }
}

const pattern = ref('');
const filteredConfigs = computed(() => {
  return props.configs.filter(config => config.name.includes(pattern.value) || config.code.includes(pattern.value));
});
</script>

<template>
  <NCard :title="$route.meta.title" :bordered="false" size="small" class="h-full">
    <template #header-extra>
      <NButton :focusable="false" quaternary @click="handleAdd">
        <icon-ph-plus class="text-icon" />
      </NButton>
    </template>
    <NInput v-model:value="pattern" placeholder="搜索" />
    <NScrollbar class="m-t-12px">
      <NList v-if="filteredConfigs.length > 0" :show-divider="false" hoverable clickable>
        <NListItem
          v-for="(config, index) in filteredConfigs"
          :key="`config-${index}`"
          :class="{ active: index === current }"
          @click="filterByDict(index)"
        >
          <template #suffix>
            <NDropdown :options="operateOptions" size="small" @select="(key: string) => handleOperate(key, config)">
              <NButton size="tiny" :focusable="false" quaternary>
                <icon-ph-dots-three class="text-icon" />
              </NButton>
            </NDropdown>
          </template>
          <div class="flex items-center gap-x-6px">
            <span class="select-none">{{ config.name }}</span>
            <span class="select-none text-gray-300">-</span>
            <span :class="config.status ? '' : 'line-through'">{{ config.code }}</span>
          </div>
        </NListItem>
      </NList>
      <NEmpty v-else description="无数据" />
    </NScrollbar>
    <ConfigModal
      v-model:visible="modalVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="emit('search')"
    />
  </NCard>
</template>

<style lang="scss" scoped>
:deep(.n-card__content) {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
}
.n-list-item {
  margin-bottom: 8px;
  padding: 8px 16px !important;
  &.active {
    color: rgb(var(--primary-color));
    background-color: rgb(var(--primary-500-color) / 0.1);
  }
}
</style>
