<script setup lang="ts">
import { computed } from 'vue';
import { createReusableTemplate } from '@vueuse/core';

defineOptions({
  name: 'CardData'
});

interface CardData {
  key: string;
  title: string;
  value: number;
  prefix?: string;
  suffix?: string;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

const cardData = computed<CardData[]>(() => [
  {
    key: 'companyCount',
    title: '企业库',
    value: 9725,
    suffix: '家',
    color: {
      start: '#13c0ea',
      end: '#548efa'
    },
    icon: 'ph:buildings'
  },
  {
    key: 'talentCount',
    title: '人才库',
    value: 1026,
    suffix: '人',
    color: {
      start: '#0cdab2',
      end: '#21c1be'
    },
    icon: 'ph:user-focus'
  },
  {
    key: 'expertCount',
    title: '专家库',
    value: 925,
    suffix: '人',
    color: {
      start: '#ecc226',
      end: '#f8992a'
    },
    icon: 'ph:user-circle'
  },
  {
    key: 'knowledgeCount',
    title: '知识库',
    value: 970925,
    suffix: '条',
    color: {
      start: '#9b8ef6',
      end: '#7863f4'
    },
    icon: 'ph:book-open-text'
  }
]);

interface GradientBgProps {
  gradientColor: string;
}

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}
</script>

<template>
  <NCard :bordered="false" class="overflow-hidden card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="py-40px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="12" :y-gap="12">
      <NGi v-for="(item, index) in cardData" :key="index" :class="`animated-fade-up-${index}`">
        <div class="flex">
          <GradientBg :gradient-color="getGradientColor(item.color)" class="w-40% flex-center b-rd-tr-40px">
            <SvgIcon :icon="item.icon" class="text-40px" />
          </GradientBg>
          <div class="w-60% flex-center flex-col">
            <CountTo
              :prefix="item.prefix"
              :suffix="item.suffix"
              :start-value="1"
              :end-value="item.value"
              class="text-24px text-primary"
            />
            <h3 class="pt-5px text-14px dark:text-white">{{ item.title }}</h3>
          </div>
        </div>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style lang="scss" scoped>
:deep(.card-wrapper) {
  .n-card__content {
    padding: 0;
  }
}
</style>
