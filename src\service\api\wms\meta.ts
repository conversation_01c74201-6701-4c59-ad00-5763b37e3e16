import { request } from '@/service/request';

export const metaApi = {
  // 获取分类列表
  list: (params: Api.Wms.MetaSearchParams) =>
    request<Api.Wms.MetaList>({
      url: '/wms/metas',
      params
    }),

  // 创建分类
  add: (data: Api.Wms.MetaCreateParams) =>
    request<null>({
      url: '/wms/metas',
      method: 'POST',
      data
    }),

  // 更新分类
  save: (data: Api.Wms.MetaUpdateParams) =>
    request<null>({
      url: `/wms/metas/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除分类
  del: (id: number) =>
    request<null>({
      url: `/wms/metas/${id}`,
      method: 'DELETE'
    }),

  // 批量删除分类
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/metas',
      method: 'DELETE',
      data: { ids }
    })
};
