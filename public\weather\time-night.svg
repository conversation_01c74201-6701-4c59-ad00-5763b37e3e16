<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512"><defs><linearGradient id="a" x1="38.8" x2="133.4" y1="20.8" y2="184.6" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#86c3db"/><stop offset=".5" stop-color="#86c3db"/><stop offset="1" stop-color="#5eafcf"/></linearGradient><linearGradient id="b" x1="294" x2="330" y1="112.8" y2="175.2" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fcd966"/><stop offset=".5" stop-color="#fcd966"/><stop offset="1" stop-color="#fccd34"/></linearGradient><linearGradient xlink:href="#b" id="c" x1="295.5" x2="316.5" y1="185.9" y2="222.1"/><linearGradient xlink:href="#b" id="d" x1="356.3" x2="387.7" y1="194.8" y2="249.2"/><symbol id="e" overflow="visible" viewBox="0 0 192.5 192.5"><path fill="url(#a)" stroke="#72b9d5" stroke-linecap="round" stroke-linejoin="round" stroke-width="4.5" d="M179.7 120.2a95 95 0 01-95.5-94.3 93.2 93.2 0 013.1-23.7A94.8 94.8 0 002.3 96a95 95 0 0095.5 94.3c44.5 0 81.8-30 92.4-70.6a98.4 98.4 0 01-10.5.6Z"/></symbol></defs><path fill="#374251" d="M233.8 432.3q-6 7.2-16.3 7.2t-16.3-7.2q-6-7.3-6-19.7t6-19.4q6-7.2 16.3-7.2 10.2 0 16.3 7.2t6 19.4q0 12.4-6 19.7Zm-16.3-3.2q9.4 0 9.4-16.4t-9.4-16.3q-9.4 0-9.4 16.3t9.4 16.4Zm48.8-5.8h-20.6v-9.9h20.6Zm38.4-12.6a13.4 13.4 0 018 4.3 12.3 12.3 0 013 8.3 15 15 0 01-5.5 11.8q-5.4 4.7-15.1 4.7-10.2 0-15.7-5t-5.9-14h12.3q.5 8.8 8.8 8.8a8.7 8.7 0 005.7-1.8 6 6 0 002.2-4.9 5.6 5.6 0 00-2.3-4.8 10.5 10.5 0 00-6.3-1.6h-3.6v-9h3.8a8 8 0 005.2-1.5 5.2 5.2 0 001.9-4.3 5.4 5.4 0 00-1.9-4.4 7.9 7.9 0 00-5.2-1.5 7 7 0 00-5 1.6q-1.7 1.7-2 5.4h-12q.7-16.9 19.5-17 8.8 0 13.8 4a12 12 0 015 10 11.1 11.1 0 01-2.3 7 11.3 11.3 0 01-6.4 4Z"/><path fill="url(#b)" stroke="#fcd34d" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m282.8 162.8 25-6.4a1.8 1.8 0 011.7.5l18.3 18a1.8 1.8 0 003-1.7l-6.4-25a1.8 1.8 0 01.5-1.7l18-18.4a1.8 1.8 0 00-1.8-3l-24.9 6.5a1.8 1.8 0 01-1.7-.5l-18.4-18a1.8 1.8 0 00-3 1.7l6.5 25a1.8 1.8 0 01-.5 1.7l-18 18.3a1.8 1.8 0 001.7 3Z"><animateTransform additive="sum" attributeName="transform" calcMode="spline" dur="6s" keySplines=".42, 0, .58, 1; .42, 0, .58, 1" repeatCount="indefinite" type="rotate" values="-15 312 144; 15 312 144; -15 312 144"/><animate attributeName="opacity" calcMode="spline" dur="6s" keySplines=".42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1" repeatCount="indefinite" values="1; .75; 1; .75; 1; .75; 1"/></path><path fill="url(#c)" stroke="#fcd34d" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m285.4 193.4 12 12.3a1.2 1.2 0 01.3 1.1l-4.3 16.6a1.2 1.2 0 002 1.2l12.3-12a1.2 1.2 0 011.1-.3l16.6 4.3a1.2 1.2 0 001.2-2l-12-12.3a1.2 1.2 0 01-.3-1.1l4.3-16.6a1.2 1.2 0 00-2-1.2l-12.3 12a1.2 1.2 0 01-1.1.3l-16.7-4.3a1.2 1.2 0 00-1.1 2Z"><animateTransform additive="sum" attributeName="transform" begin="-.33s" calcMode="spline" dur="6s" keySplines=".42, 0, .58, 1; .42, 0, .58, 1" repeatCount="indefinite" type="rotate" values="-15 306 204; 15 306 204; -15 306 204"/><animate attributeName="opacity" begin="-.33s" calcMode="spline" dur="6s" keySplines=".42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1" repeatCount="indefinite" values="1; .75; 1; .75; 1; .75; 1"/></path><path fill="url(#d)" stroke="#fcd34d" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m337.3 223.7 24.8 7a1.8 1.8 0 011.3 1.2l6.9 24.8a1.8 1.8 0 003.4 0l7-24.8a1.8 1.8 0 011.2-1.3l24.8-6.9a1.8 1.8 0 000-3.4l-24.8-7a1.8 1.8 0 01-1.3-1.2l-6.9-24.8a1.8 1.8 0 00-3.4 0l-7 24.8a1.8 1.8 0 01-1.2 1.3l-24.8 6.9a1.8 1.8 0 000 3.4Z"><animateTransform additive="sum" attributeName="transform" begin="-.67s" calcMode="spline" dur="6s" keySplines=".42, 0, .58, 1; .42, 0, .58, 1" repeatCount="indefinite" type="rotate" values="-15 372 222; 15 372 222; -15 372 222"/><animate attributeName="opacity" begin="-.67s" calcMode="spline" dur="6s" keySplines=".42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1; .42, 0, .58, 1" repeatCount="indefinite" values="1; .75; 1; .75; 1; .75; 1"/></path><use xlink:href="#e" width="192.5" height="192.5" transform="translate(159.76 139.76)"><animateTransform additive="sum" attributeName="transform" dur="6s" repeatCount="indefinite" type="rotate" values="-15 96.24 96.24; 9 96.24 96.24; -15 96.24 96.24"/></use></svg>