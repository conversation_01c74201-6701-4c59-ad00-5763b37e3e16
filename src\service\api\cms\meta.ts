import { request } from '@/service/request';

export const metaApi = {
  // 获取栏目列表
  list: (params: Api.Cms.MetaSearchParams) =>
    request<Api.Cms.MetaList>({
      url: '/cms/metas',
      params
    }),

  // 创建栏目
  add: (data: Api.Cms.MetaCreateParams) =>
    request<null>({
      url: '/cms/metas',
      method: 'POST',
      data
    }),

  // 更新栏目
  save: (data: Api.Cms.MetaUpdateParams) =>
    request<null>({
      url: `/cms/metas/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除栏目
  del: (id: number) =>
    request<null>({
      url: `/cms/metas/${id}`,
      method: 'DELETE'
    }),

  // 批量删除栏目
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/cms/metas',
      method: 'DELETE',
      data: { ids }
    })
};
