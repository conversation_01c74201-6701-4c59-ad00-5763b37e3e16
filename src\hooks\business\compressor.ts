import Compressor from 'compressorjs';

export type CompressorOptions = {
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
};

export function useCompressor(image: File, options: CompressorOptions = {}): Promise<File> {
  return new Promise((resolve, reject) => {
    // Check file type
    if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(image.type)) {
      reject(new Error(`Unsupported file type: ${image.type}.`));
      return;
    }

    // Change ext to webp
    const filename = [...image.name.split('.').slice(0, -1), 'webp'].join('.');

    // 创建并调用Compressor，但不需要保存实例
    // eslint-disable-next-line no-new
    new Compressor(image, {
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1920,
      convertTypes: ['image/webp'],
      ...options,
      success(result) {
        resolve(new File([result], filename, { type: 'image/webp' }));
      },
      error(err) {
        reject(err);
      }
    });
  });
}
