<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="#2a81bf" d="M293.5 298a7.7 7.7 0 00-5.3 2L256 330.9 223.8 300a7.8 7.8 0 00-10.6 0 6.9 6.9 0 000 10l37.5 35.8a7.8 7.8 0 0010.6 0l37.5-35.7a6.9 6.9 0 000-10.1 7.7 7.7 0 00-5.3-2.1Z" opacity="0"><animateTransform attributeName="transform" begin="-1.3s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 -30; 0 0; 0 30"/><animate attributeName="opacity" begin="-1.3s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path><path fill="#2a81bf" d="M293.5 253a7.7 7.7 0 00-5.3 2L256 285.9 223.8 255a7.8 7.8 0 00-10.6 0 6.9 6.9 0 000 10l37.5 35.8a7.8 7.8 0 0010.6 0l37.5-35.7a6.9 6.9 0 000-10.1 7.7 7.7 0 00-5.3-2.1Z" opacity="0"><animateTransform attributeName="transform" begin="-1.2s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 -30; 0 0; 0 30"/><animate attributeName="opacity" begin="-1.2s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path><path fill="#2a81bf" d="M293.5 208a7.7 7.7 0 00-5.3 2L256 240.9 223.8 210a7.8 7.8 0 00-10.6 0 6.9 6.9 0 000 10l37.5 35.8a7.8 7.8 0 0010.6 0l37.5-35.7a6.9 6.9 0 000-10.1 7.7 7.7 0 00-5.3-2.1Z" opacity="0"><animateTransform attributeName="transform" begin="-1.1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 -30; 0 0; 0 30"/><animate attributeName="opacity" begin="-1.1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path><path fill="#2a81bf" d="M293.5 163a7.7 7.7 0 00-5.3 2L256 195.9 223.8 165a7.8 7.8 0 00-10.6 0 6.9 6.9 0 000 10l37.5 35.8a7.8 7.8 0 0010.6 0l37.5-35.7a6.9 6.9 0 000-10.1 7.7 7.7 0 00-5.3-2.1Z" opacity="0"><animateTransform attributeName="transform" begin="-1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" type="translate" values="0 -30; 0 0; 0 30"/><animate attributeName="opacity" begin="-1s" calcMode="spline" dur="3s" keySplines=".55, 0, .1, 1; .55, 0, .1, 1" repeatCount="indefinite" values="0; 1; 0"/></path></svg>