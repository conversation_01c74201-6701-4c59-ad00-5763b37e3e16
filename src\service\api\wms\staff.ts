import { request } from '@/service/request';

export const staffApi = {
  // 获取员工列表
  list: (params: Api.Wms.StaffSearchParams) =>
    request<Api.Wms.StaffList>({
      url: '/wms/staffs',
      params
    }),

  // 创建员工
  add: (data: Api.Wms.StaffCreateParams) =>
    request<null>({
      url: '/wms/staffs',
      method: 'POST',
      data
    }),

  // 更新员工
  save: (data: Api.Wms.StaffUpdateParams) =>
    request<null>({
      url: `/wms/staffs/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除员工
  del: (id: number) =>
    request<null>({
      url: `/wms/staffs/${id}`,
      method: 'DELETE'
    }),

  // 批量删除员工
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/staffs',
      method: 'DELETE',
      data: { ids }
    }),

  // 重置密码
  password: (data: Api.Wms.StaffPasswordParams) =>
    request<null>({
      url: `/wms/staffs/${data.id}`,
      method: 'PATCH',
      data
    })
};
