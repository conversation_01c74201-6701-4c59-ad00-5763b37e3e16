<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import CardData from './modules/card-data.vue';
import LineChart from './modules/line-chart.vue';
import Pie<PERSON>hart from './modules/pie-chart.vue';
import SystemLog from './modules/system-log.vue';
import CreativityBanner from './modules/creativity-banner.vue';
import CityWeather from './modules/city-weather.vue';

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 12));
</script>

<template>
  <NSpace vertical :size="12">
    <NGrid :x-gap="gap" :y-gap="12" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:17">
        <CardData />
        <LineChart class="mt-12px" />
      </NGi>
      <NGi span="24 s:24 m:7">
        <CityWeather />
        <PieChart class="mt-12px" />
      </NGi>
    </NGrid>
    <NGrid :x-gap="gap" :y-gap="12" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:12">
        <SystemLog />
      </NGi>
      <NGi span="24 s:24 m:12">
        <CreativityBanner />
      </NGi>
    </NGrid>
  </NSpace>
</template>

<style scoped></style>
