/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** option */
    type Option = {
      label: string;
      value: string;
    }

    /**
     * gender
     *
     * - 0: "unknown"
     * - 1: "male"
     * - 2: "female"
     */
    type Gender = 0 | 1 | 2;

    /** http method */
    type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

    /** upload file */
    type UploadFile = {
      id: string;
      name: string;
      size: number;
      type: string;
      url: string;
    };

    /** common record */
    type Record<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createdBy: string;
      /** record create time */
      createdAt: string;
      /** record updater */
      updatedBy: string;
      /** record update time */
      updatedAt: string;
      /** record status */
      status: boolean;
    } & T;

    /** common params of paginating query list data */
    interface PaginatingRecord<T = any> {
      records: T[];
      total: number;
    }

    /** common params of paginating */
    interface PaginatingParams {
      /** current page number */
      _page: number;
      /** page size */
      _limit: number;
    }

    /** common search params of table */
    type SearchParams<T = any> = CommonType.RecordNullable<
      T &
        PaginatingParams & {
          _sort?: string;
          _order?: string;
          _embed?: string;
          _expand?: string;
          q?: string;
        }
    >;

    /** common create params of table */
    type CreateParams<T = any> = CommonType.RecordNullable<
      Omit<T, 'id' | 'createBy' | 'createAt' | 'updateBy' | 'updateAt'>
    >;

    /** common update params of table */
    type UpdateParams<T = any> = CommonType.RecordNullable<Omit<T, 'createBy' | 'createAt' | 'updateBy' | 'updateAt'>>;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginParams {
      tenant: string;
      username: string;
      password: string;
    }

    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      id: string;
      username: string;
      nickname: string;
      gender: Common.Gender;
      avatar: string;
      email: string;
      phone: string;
      roles?: System.Role[];
      buttons?: string[];
      tenant?: System.Tenant;
    }

    interface UpdateUserInfoParams {
      nickname: string;
      avatar: string;
      gender: Common.Gender;
      email: string;
      phone: string;
    }

    interface UpdatePasswordParams {
      oldpass: string;
      password: string;
      repass: string;
    }

    interface CustomBackendErrorParams {
      code: string;
      message: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }
  }

  /**
   * namespace System
   *
   * backend api module: "system"
   */
  namespace System {
    /** config */
    type Config = Common.Record<{
      name: string;
      code: string;
      summary: string;
      params: ConfigParam[];
    }>;

    /** config param */
    type ConfigParam = {
      id: string;
      name: string;
      code: string;
      value: string;
      summary: string;
      status: boolean;
    };

    /** config list */
    type ConfigList = Common.PaginatingRecord<Config>;

    /** config search params */
    type ConfigSearchParams = Common.SearchParams<Pick<Config, 'name' | 'code' | 'status'>>;

    /** config create params */
    type ConfigCreateParams = Common.CreateParams<Config>;

    /** config update params */
    type ConfigUpdateParams = Common.UpdateParams<Config>;

    /** dict */
    type Dict = Common.Record<{
      name: string;
      code: string;
      summary: string;
      options?: DictOption[];
    }>;

    /** dict option */
    type DictOption = {
      id: string;
      label: string;
      value: string;
      type: 'default' | 'success' | 'error' | 'warning' | 'primary' | 'info';
      status: boolean;
    };

    /** dict list */
    type DictList = Common.PaginatingRecord<Dict>;

    /** dict search params */
    type DictSearchParams = Common.SearchParams<Pick<Dict, 'name' | 'code' | 'status'>>;

    /** dict create params */
    type DictCreateParams = Common.CreateParams<Dict>;

    /** dict update params */
    type DictUpdateParams = Common.UpdateParams<Dict>;

    /**
     * menu type
     *
     * - 1: dir
     * - 2: menu
     */
    type MenuType = 1 | 2;

    /** menu layout */
    type MenuLayout = 'default' | 'base' | 'blank';

    /** menu */
    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      'order' | 'href' | 'constant' | 'hideInMenu' | 'keepAlive' | 'multiTab'
    >;

    /** menu */
    type Menu = Common.Record<{
      menuType: MenuType;
      menuName: string;
      routeName: string;
      routePath: string;
      layout: MenuLayout;
      component: string;
      icon: string;
      parentId: number;
      children?: Menu[];
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingRecord<Menu>;

    /** menu search params */
    type MenuSearchParams = Common.SearchParams<Pick<Menu, 'menuName' | 'routeName' | 'constant' | 'status'>>;

    /** menu create params */
    type MenuCreateParams = Common.CreateParams<Menu>;

    /** menu update params */
    type MenuUpdateParams = Common.UpdateParams<Menu>;

    /** api */
    type Api = Common.Record<{
      path: string;
      method: Common.HttpMethod;
      tags: string[];
      summary: string;
    }>;

    /** api list */
    type ApiList = Common.PaginatingRecord<Api>;

    /** api search params */
    type ApiSearchParams = Common.SearchParams<Pick<Api, 'path' | 'method' | 'status'>>;

    /** api create params */
    type ApiCreateParams = Common.CreateParams<Api>;

    /** api update params */
    type ApiUpdateParams = Common.UpdateParams<Api>;

    /** dept */
    type Dept = Common.Record<{
      name: string;
      code: string;
      summary: string;
      order: number;
      parentId: number;
    }>;

    /** dept list */
    type DeptList = Common.PaginatingRecord<Dept>;

    /** dept search params */
    type DeptSearchParams = Common.SearchParams<Pick<Dept, 'name' | 'code' | 'status'>>;

    /** dept create params */
    type DeptCreateParams = Common.CreateParams<Dept>;

    /** dept update params */
    type DeptUpdateParams = Common.UpdateParams<Dept>;

    /** tenant */
    type Tenant = Common.Record<{
      name: string;
      code: string;
      summary: string;
      expiredAt: string;
    }>;

    /** tenant list */
    type TenantList = Common.PaginatingRecord<Tenant>;

    /** tenant search params */
    type TenantSearchParams = Common.SearchParams<Pick<Tenant, 'name' | 'code' | 'status'>>;

    /** tenant create params */
    type TenantCreateParams = Common.CreateParams<Tenant>;

    /** tenant update params */
    type TenantUpdateParams = Common.UpdateParams<Tenant>;

    /** role */
    type Role = Common.Record<{
      name: string;
      code: string;
      summary: string;
      home?: string | null;
      menuIds?: number[];
      apiIds?: number[];
    }>;

    /** role list */
    type RoleList = Common.PaginatingRecord<Role>;

    /** role search params */
    type RoleSearchParams = Common.SearchParams<Pick<Role, 'name' | 'code' | 'status'>>;

    /** role create params */
    type RoleCreateParams = Common.CreateParams<Role>;

    /** role update params */
    type RoleUpdateParams = Common.UpdateParams<Role>;

    /** role permit params */
    type RolePermitParams = CommonType.RecordNullable<Pick<Role, 'id' | 'home' | 'menuIds' | 'apiIds'>>;

    /** user */
    type User = Common.Record<{
      username: string;
      password?: string;
      nickname: string;
      gender: Common.Gender;
      phone: string;
      email: string;
      avatar: string;
      roleIds: number[];
      tenantId: number | null;
      roles?: Role[];
      tenant?: Tenant;
    }>;

    /** user list */
    type UserList = Common.PaginatingRecord<User>;

    /** user search params */
    type UserSearchParams = Common.SearchParams<
      Pick<User, 'username' | 'nickname' | 'gender' | 'phone' | 'email' | 'status' | 'tenantId'>
    >;

    /** user create params */
    type UserCreateParams = Common.CreateParams<User>;

    /** user update params */
    type UserUpdateParams = Common.UpdateParams<User>;

    /** user password params */
    type UserPasswordParams = CommonType.RecordNullable<Pick<User, 'id' | 'password'>>;

    /** log */
    type Log = Common.Record<{
      userId: number;
      path: string;
      method: Common.HttpMethod;
      code: number;
      params: object;
      time: number;
      userAgent: string;
      clientIp: string;
      user?: User;
    }>;

    /** log list */
    type LogList = Common.PaginatingRecord<Log>;

    /** log search params */
    type LogSearchParams = Common.SearchParams<Pick<Log, 'userId' | 'path' | 'method' | 'code' | 'clientIp'>>;
  }

  /**
   * namespace Cms
   *
   * backend api module: "cms"
   */
  namespace Cms {
    /** meta */
    type Meta = Common.Record<{
      name: string;
      slug: string;
      cover: string;
      order: number;
      summary: string;
      parentId: number;
    }>;

    /** meta list */
    type MetaList = Common.PaginatingRecord<Meta>;

    /** meta search params */
    type MetaSearchParams = Common.SearchParams<Pick<Meta, 'name' | 'slug' | 'status'>>;

    /** meta create params */
    type MetaCreateParams = Common.CreateParams<Meta>;

    /** meta update params */
    type MetaUpdateParams = Common.UpdateParams<Meta>;

    /** post */
    type Post = Common.Record<{
      metaId: number;
      title: string;
      summary: string;
      content: string;
      slug: string;
      cover: string;
      files: Common.UploadFile[];
      author: string;
      from: string;
      password: string;
      tags: string[];
      order: number;
      flag: number[];
      meta?: Meta;
    }>;

    /** post list */
    type PostList = Common.PaginatingRecord<Post>;

    /** post search params */
    type PostSearchParams = Common.SearchParams<Pick<Post, 'title' | 'slug' | 'flag' | 'metaId' | 'status'>>;

    /** post create params */
    type PostCreateParams = Common.CreateParams<Post>;

    /** post update params */
    type PostUpdateParams = Common.UpdateParams<Post>;
  }

  /**
   * namespace Wms
   *
   * backend api module: "wms"
   */
  namespace Wms {
    /** area */
    type Area = Common.Record<{
      name: string;
      code: string;
      type: number | null;
      summary: string;
      order: number;
      parentId: number;
      parentPath: number[];
    }>;

    /** area list */
    type AreaList = Common.PaginatingRecord<Area>;

    /** area search params */
    type AreaSearchParams = Common.SearchParams<
      Pick<Area, 'name' | 'code' | 'type' | 'status'>
    >;

    /** area create params */
    type AreaCreateParams = Common.CreateParams<Area>;

    /** area update params */
    type AreaUpdateParams = Common.UpdateParams<Area>;

    /** staff */
    type Staff = Common.Record<{
      username: string;
      password?: string;
      nickname: string;
      gender: Common.Gender;
      phone: string;
      email: string;
      avatar: string;
      roleIds: number[];
    }>;

    /** staff list */
    type StaffList = Common.PaginatingRecord<Staff>;

    /** staff search params */
    type StaffSearchParams = Common.SearchParams<
      Pick<Staff, 'username' | 'nickname' | 'gender' | 'phone' | 'email' | 'status'>
    >;

    /** staff create params */
    type StaffCreateParams = Common.CreateParams<Staff>;

    /** staff update params */
    type StaffUpdateParams = Common.UpdateParams<Staff>;

    /** staff password params */
    type StaffPasswordParams = CommonType.RecordNullable<Pick<Staff, 'id' | 'password'>>;

    /** partner */
    type Partner = Common.Record<{
      name: string;
      type: number[];
      level: number;
      summary: string;
      contact: string;
      phone: string;
      email: string;
      area: string | null;
      address: string;
      bankName: string;
      bankAccount: string;
      order: number;
    }>;

    /** partner list */
    type PartnerList = Common.PaginatingRecord<Partner>;

    /** partner search params */
    type PartnerSearchParams = Common.SearchParams<
      Pick<Partner, 'name' | 'type' | 'status'>
    >;

    /** partner create params */
    type PartnerCreateParams = Common.CreateParams<Partner>;

    /** partner update params */
    type PartnerUpdateParams = Common.UpdateParams<Partner>;

    /** item */
    type Item = Common.Record<{
      name: string;
      code: string;
      summary: string;
      order: number;
      metaId: number;
    }>;

    /** item list */
    type ItemList = Common.PaginatingRecord<Item>;

    /** item search params */
    type ItemSearchParams = Common.SearchParams<
      Pick<Item, 'name' | 'code' | 'status' | 'metaId'>
    >;

    /** item create params */
    type ItemCreateParams = Common.CreateParams<Item>;

    /** item update params */
    type ItemUpdateParams = Common.UpdateParams<Item>;

    /** meta */
    type Meta = Common.Record<{
      name: string;
      order: number;
      parentId: number;
      parentPath: number[];
    }>;

    /** meta list */
    type MetaList = Common.PaginatingRecord<Meta>;

    /** meta search params */
    type MetaSearchParams = Common.SearchParams<
      Pick<Meta, 'name' | 'status' | 'parentId'>
    >;

    /** meta create params */
    type MetaCreateParams = Common.CreateParams<Meta>;

    /** meta update params */
    type MetaUpdateParams = Common.UpdateParams<Meta>;

    /** unit */
    type Unit = Common.Record<{
      name: string;
      order: number;
    }>;

    /** unit list */
    type UnitList = Common.PaginatingRecord<Unit>;

    /** unit search params */
    type UnitSearchParams = Common.SearchParams<
      Pick<Unit, 'name' | 'status'>
    >;

    /** unit create params */
    type UnitCreateParams = Common.CreateParams<Unit>;

    /** unit update params */
    type UnitUpdateParams = Common.UpdateParams<Unit>;

    /** sku */
    type Sku = Common.Record<{
      name: string;
      code: string;
      unit: string;
      min: number;
      summary: string;
      attrs: Common.Option[];
      order: number;
      itemId: number;
    }>;

    /** sku list */
    type SkuList = Common.PaginatingRecord<Sku>;

    /** sku search params */
    type SkuSearchParams = Common.SearchParams<
      Pick<Sku, 'name' | 'code' | 'status'>
    >;

    /** sku create params */
    type SkuCreateParams = Common.CreateParams<Sku>;

    /** sku update params */
    type SkuUpdateParams = Common.UpdateParams<Sku>;
  }
}
