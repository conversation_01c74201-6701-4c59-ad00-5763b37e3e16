import { request } from '@/service/request';

export const postApi = {
  // 获取文章列表
  list: (params: Api.Cms.PostSearchParams) =>
    request<Api.Cms.PostList>({
      url: '/cms/posts',
      params
    }),

  // 创建文章
  add: (data: Api.Cms.PostCreateParams) =>
    request<null>({
      url: '/cms/posts',
      method: 'POST',
      data
    }),

  // 更新文章
  save: (data: Api.Cms.PostUpdateParams) =>
    request<null>({
      url: `/cms/posts/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除文章
  del: (id: number) =>
    request<null>({
      url: `/cms/posts/${id}`,
      method: 'DELETE'
    }),

  // 批量删除文章
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/cms/posts',
      method: 'DELETE',
      data: { ids }
    })
};
