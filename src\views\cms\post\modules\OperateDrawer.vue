<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { postApi } from '@/service/api/cms';
import { useAuthStore } from '@/store/modules/auth';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import WangEditor from '@/components/custom/wang-editor.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Cms.Post | null;
  /** the meta options */
  metaOptions?: TreeOption[];
  /** the meta id */
  metaId?: number | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const authStore = useAuthStore();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增文章',
    edit: '编辑文章'
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Cms.Post,
  | 'title'
  | 'slug'
  | 'metaId'
  | 'cover'
  | 'summary'
  | 'content'
  | 'status'
  | 'author'
  | 'from'
  | 'password'
  | 'tags'
  | 'order'
  | 'flag'
  | 'files'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    title: '',
    slug: '',
    metaId: props.metaId || 0,
    cover: '',
    summary: '',
    content: '',
    status: true,
    author: authStore.userInfo.nickname || authStore.userInfo.nickname,
    from: '',
    password: '',
    tags: [],
    order: 0,
    flag: [],
    files: []
  };
}

type RuleKey = Extract<keyof Model, 'title' | 'metaId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  title: defaultRequiredRule,
  metaId: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await postApi.save(model.value) : await postApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="800">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NFormItem label="标题" path="title">
              <NInput v-model:value="model.title" placeholder="请输入标题" clearable />
            </NFormItem>
            <NGrid :cols="2" :x-gap="12">
              <NFormItemGi label="栏目" path="metaId">
                <NTreeSelect
                  v-model:value="model.metaId"
                  :options="metaOptions"
                  key-field="id"
                  label-field="name"
                  clearable
                />
              </NFormItemGi>
              <NFormItemGi label="别名" path="slug">
                <NInput v-model:value="model.slug" placeholder="请输入别名" clearable />
              </NFormItemGi>
              <NFormItemGi label="作者" path="author">
                <NInput v-model:value="model.author" placeholder="请输入作者" clearable />
              </NFormItemGi>
              <NFormItemGi label="来源" path="from">
                <NInput v-model:value="model.from" placeholder="请输入来源" clearable />
              </NFormItemGi>
            </NGrid>
            <NFormItem label="封面" path="cover">
              <UploadCover v-model:value="model.cover" />
            </NFormItem>
            <NFormItem label="摘要" path="summary">
              <NInput v-model:value="model.summary" placeholder="请输入摘要" type="textarea" clearable />
            </NFormItem>
            <NFormItem label="标签" path="tags">
              <NDynamicTags v-model:value="model.tags" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="content" tab="编辑正文">
            <WangEditor v-model:value="model.content" />
          </NTabPane>
          <NTabPane name="files" tab="上传附件">
            <UploadFiles v-model:value="model.files" />
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="密码" path="password">
              <NInput
                v-model:value="model.password"
                type="password"
                placeholder="请输入密码"
                show-password-on="mousedown"
                clearable
              />
            </NFormItem>
            <NFormItem label="标志" path="flag">
              <NSelect
                v-model:value="model.flag"
                placeholder="请选择标志"
                :options="useDict('number').items('Flag')"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
