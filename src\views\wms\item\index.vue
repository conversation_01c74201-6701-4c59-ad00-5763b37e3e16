<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import Item from './modules/item/index.vue';
import Sku from './modules/sku/index.vue';

const route = useRoute();

const modules = {
  default: { label: '物料管理', component: Item },
  sku: { label: '规格管理', component: Sku }
}

const active = computed(() => route.query.id ? modules.sku : modules.default);

// 使用路由的完整路径作为 key，确保组件能正确切换
const componentKey = computed(() => {
  return route.query.id ? `sku-${route.query.id}` : 'item-default';
});
</script>

<template>
  <Transition name="fade" mode="out-in">
    <component :is="active.component" :key="componentKey" />
  </Transition>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
