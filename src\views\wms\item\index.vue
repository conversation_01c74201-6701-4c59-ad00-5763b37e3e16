<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import Item from './modules/item/index.vue';
import Sku from './modules/sku/index.vue';

const route = useRoute();

const modules = {
  default: { label: '物料管理', component: Item },
  sku: { label: '规格管理', component: Sku }
}

const active = computed(() => route.query.id ? modules.sku : modules.default);
</script>

<template>
  <component :is="active.component" :key="active.label" />
</template>

<style scoped></style>
