<script setup lang="ts">
import { ref } from 'vue';
import Item from './modules/item/index.vue';
import Sku from './modules/sku/index.vue';
import emitter from '@/utils/mitt';

const item = ref<Api.Wms.Item | null>(null)

emitter.on('showSku', (data: Api.Wms.Item) => {
  item.value = data
})
</script>

<template>
  <div class="min-h-500px">
    <Item v-show="!item" />
    <Sku v-show="item" :item="item" />
  </div>
</template>

<style scoped></style>
