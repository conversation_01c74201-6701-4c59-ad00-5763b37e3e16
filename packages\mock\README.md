# JSON-Server 模拟 API 应用

这是一个基于 json-server 的 RESTful API 模拟服务器，用于前端开发和测试。

## 功能特点

- 基于模块化的数据结构（按模块区分）
- 完整支持 json-server 的 \_embed 和 \_expand 等功能
- 使用默认路由实现
- 支持过滤、排序、分页等查询参数

## 安装

```bash
# 使用pnpm安装依赖
pnpm install
```

## 启动服务器

```bash
# 开发模式（自动重启）
pnpm dev

# 生产模式
pnpm start
```

## API 使用说明

服务器启动后，API 根路径为：`http://localhost:9999/api`

### 可用资源

#### 博客模块 (`/blog`)

- `/blog/posts` - 博客文章
- `/blog/cates` - 文章分类
- `/blog/comments` - 文章评论

#### 系统模块 (`/system`)

- `/system/users` - 系统用户
- `/system/roles` - 用户角色
- `/system/depts` - 部门信息

### 支持的查询参数

- **嵌入关联资源**：`_embed=resource`
  例如：`/blog/posts?_embed=comments`

- **展开关联资源**：`_expand=resource`
  例如：`/blog/comments?_expand=post`

- **分页**：`_page=n&_limit=m`
  例如：`/blog/posts?_page=1&_limit=10`

- **排序**：`_sort=field&_order=asc|desc`
  例如：`/blog/posts?_sort=createdAt&_order=desc`

- **过滤**：`field=value`
  例如：`/blog/posts?author=张三`

### 完整的 CRUD 操作

- **GET**：获取资源

  - `GET /blog/posts` - 获取所有文章
  - `GET /blog/posts/1` - 获取 ID 为 1 的文章

- **POST**：创建资源

  - `POST /blog/posts` - 创建新文章

- **PUT/PATCH**：更新资源

  - `PUT /blog/posts/1` - 替换 ID 为 1 的文章
  - `PATCH /blog/posts/1` - 部分更新 ID 为 1 的文章

- **DELETE**：删除资源
  - `DELETE /blog/posts` - 批量删除指定 IDS 文章
  - `DELETE /blog/posts/1` - 删除 ID 为 1 的文章

## 数据结构

数据文件位于`db`文件夹中，按模块分为不同的目录：

### 博客模块 (`db/blog/`)

- `posts.json` - 文章数据
- `cates.json` - 分类数据
- `comments.json` - 评论数据

### 系统模块 (`db/system/`)

- `users.json` - 用户数据
- `roles.json` - 角色数据
- `depts.json` - 部门数据

## 自定义

如需添加新的数据模块，只需在`db`文件夹中创建新的目录和相应的 JSON 文件，服务器会自动加载。
