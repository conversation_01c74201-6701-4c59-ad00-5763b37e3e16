<script setup lang="ts">
import { computed } from 'vue';
import type { VNode } from 'vue';
import { useBoolean } from '@sa/hooks';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';
import { useSvgIcon } from '@/hooks/common/icon';
import { $t } from '@/locales';
import UserInfo from './user-userinfo.vue';
import UserPassword from './user-password.vue';

defineOptions({
  name: 'UserAvatar'
});

const authStore = useAuthStore();
const { routerPushByKey, toLogin } = useRouterPush();
const { SvgIconVNode } = useSvgIcon();

function loginOrRegister() {
  toLogin();
}

type DropdownKey = 'userinfo' | 'password' | 'logout';

type DropdownOption =
  | {
      key: DropdownKey;
      label: string;
      icon?: () => VNode;
    }
  | {
      type: 'divider';
      key: string;
    };

const options = computed(() => {
  const opts: DropdownOption[] = [
    {
      label: '个人设置',
      key: 'userinfo',
      icon: SvgIconVNode({ icon: 'ph:user', fontSize: 18 })
    },
    {
      label: '修改密码',
      key: 'password',
      icon: SvgIconVNode({ icon: 'ph:lock', fontSize: 18 })
    },
    {
      label: $t('common.logout'),
      key: 'logout',
      icon: SvgIconVNode({ icon: 'ph:sign-out', fontSize: 18 })
    }
  ];

  return opts;
});

function logout() {
  window.$dialog?.info({
    title: $t('common.tip'),
    content: $t('common.logoutConfirm'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      authStore.resetStore();
    }
  });
}

const { bool: visibleUserinfo, setTrue: openUserinfoModal } = useBoolean();
const { bool: visiblePassword, setTrue: openPasswordModal } = useBoolean();

function handleDropdown(key: DropdownKey) {
  switch (key) {
    case 'userinfo':
      openUserinfoModal();
      break;
    case 'password':
      openPasswordModal();
      break;
    case 'logout':
      logout();
      break;
    default:
      // If your other options are jumps from other routes, they will be directly supported here
      routerPushByKey(key);
  }
}
</script>

<template>
  <NButton v-if="!authStore.isLogin" quaternary @click="loginOrRegister">
    {{ $t('page.login.common.loginOrRegister') }}
  </NButton>
  <NDropdown v-else placement="bottom" trigger="hover" :options="options" @select="handleDropdown">
    <div class="h-full flex-y-center">
      <ButtonIcon>
        <SvgIcon icon="ph:user-circle" class="text-icon-large" />
        <span class="text-16px font-medium">{{ authStore.userInfo.nickname || authStore.userInfo.username }}</span>
      </ButtonIcon>
    </div>
  </NDropdown>
  <UserInfo v-model:visible="visibleUserinfo" />
  <UserPassword v-model:visible="visiblePassword" />
</template>

<style scoped></style>
