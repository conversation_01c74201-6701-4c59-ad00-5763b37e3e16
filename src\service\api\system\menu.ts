import { request } from '@/service/request';

export const menuApi = {
  // 获取菜单列表
  list: (params: Api.System.MenuSearchParams) =>
    request<Api.System.MenuList>({
      url: '/system/menus',
      params
    }),

  // 创建菜单
  add: (data: Api.System.MenuCreateParams) =>
    request<null>({
      url: '/system/menus',
      method: 'POST',
      data
    }),

  // 更新菜单
  save: (data: Api.System.MenuUpdateParams) =>
    request<null>({
      url: `/system/menus/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除菜单
  del: (id: number) =>
    request<null>({
      url: `/system/menus/${id}`,
      method: 'DELETE'
    }),

  // 批量删除菜单
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/menus',
      method: 'DELETE',
      data: { ids }
    })
};
