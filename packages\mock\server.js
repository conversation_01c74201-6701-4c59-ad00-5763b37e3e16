const fs = require('node:fs');
const path = require('node:path');
const process = require('node:process');
const jsonServer = require('json-server');
const cors = require('cors');
const { customRoutes, authenticateJWT } = require('./routes');

// 创建服务器
const server = jsonServer.create();

// 跨域
server.use(cors());

// 中间件
const middlewares = jsonServer.defaults();

// 数据目录
const dbDir = path.join(__dirname, './db');

// 路由数据
const routes = {};
const rules = {};
fs.readdirSync(dbDir).forEach(module => {
  fs.readdirSync(path.join(dbDir, module)).forEach(source => {
    const resource = path.basename(source, '.json');
    routes[resource] = require(path.join(dbDir, module, source));
    rules[`/api/${module}/*`] = '/$1';
  });
});
const router = jsonServer.router(routes);

// 解析请求数据
server.use(jsonServer.bodyParser);

// 添加JWT认证中间件
server.use(authenticateJWT);

// 添加自定义路由
customRoutes(server, routes);

// 路由重写
server.use(jsonServer.rewriter(rules));

// 默认中间件
server.use(middlewares);

// 默认路由
server.use(router);

// 格式化响应数据
router.render = (_, res) => {
  const data = res.locals.data;

  // 检查响应头中是否包含X-Total-Count，这是json-server分页时会设置的头信息
  const totalCountHeader = res.get('X-Total-Count');

  // 通过检查响应头判断是否为分页请求
  if (totalCountHeader) {
    const total = Number.parseInt(totalCountHeader, 10);
    res.jsonp({
      code: 200,
      data: {
        records: data,
        total
      },
      message: 'OK'
    });
  } else {
    res.jsonp({
      code: 200,
      data,
      message: 'OK'
    });
  }
};

// 启动服务
const PORT = process.env.PORT || 9999;
server.listen(PORT, () => {
  // eslint-disable-next-line no-console
  console.log(`JSON Server is running on http://localhost:${PORT}`);
});
