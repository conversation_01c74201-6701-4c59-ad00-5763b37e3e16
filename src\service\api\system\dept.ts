import { request } from '@/service/request';

export const deptApi = {
  // 获取部门列表
  list: (params: Api.System.DeptSearchParams) =>
    request<Api.System.DeptList>({
      url: '/system/depts',
      params
    }),

  // 创建部门
  add: (data: Api.System.DeptCreateParams) =>
    request<null>({
      url: '/system/depts',
      method: 'POST',
      data
    }),

  // 更新部门
  save: (data: Api.System.DeptUpdateParams) =>
    request<null>({
      url: `/system/depts/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除部门
  del: (id: number) =>
    request<null>({
      url: `/system/depts/${id}`,
      method: 'DELETE'
    }),

  // 批量删除部门
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/depts',
      method: 'DELETE',
      data: { ids }
    })
};
