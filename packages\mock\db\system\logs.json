[{"id": 1, "userId": 1, "path": "/auth/login", "method": "POST", "code": 200, "query": "", "body": "", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "clientIp": "127.0.0.1", "createdAt": "2025-07-01T11:12:29+08:00"}, {"id": 2, "userId": 1, "path": "/system/users", "method": "POST", "code": 200, "query": "", "body": "", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "clientIp": "127.0.0.1", "createdAt": "2025-07-01T11:12:29+08:00"}, {"id": 3, "userId": 1, "path": "/system/users", "method": "DELETE", "code": 200, "query": "", "body": "", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "clientIp": "127.0.0.1", "createdAt": "2025-07-01T11:12:29+08:00"}]