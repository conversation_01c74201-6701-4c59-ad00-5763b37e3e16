@forward 'scrollbar';

@keyframes fade-up {
  0% {
    transform: translateY(60px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@for $i from 0 through 12 {
  .animated-fade-up-#{$i} {
    opacity: 0;
    animation-name: fade-up;
    animation-duration: 0.5s;
    animation-fill-mode: forwards;
    animation-delay: calc($i/10) + s;
  }
}

.n-select {
  .n-base-selection {
    .n-base-selection-tags {
      padding-left: 3px;
      .n-base-selection-tag-wrapper {
        padding-right: 3px;
      }
    }
  }
}

.n-input {
  .n-input__input-el {
    height: auto;
  }
}

.n-layout-sider {
  .n-tree {
    &.n-tree--block-line {
      .n-tree-node-content {
        .n-tree-node-content__text {
          border: 0;
        }
      }
      .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected {
        .n-tree-node-content__text {
          color: rgb(var(--primary-color));
        }
      }
    }
  }
}

.n-data-table {
  .n-data-table-td[data-col-key='drag'] {
    cursor: move;
  }
  .sortable-ghost,
  .sortable-drag {
    background-color: rgba(var(--primary-color), 0.1) !important;
  }
  .sortable-drag {
    opacity: 0.8;
  }
}
