import { request } from '@/service/request';

export const unitApi = {
  // 获取单位列表
  list: (params: Api.Wms.UnitSearchParams) =>
    request<Api.Wms.UnitList>({
      url: '/wms/units',
      params
    }),

  // 创建单位
  add: (data: Api.Wms.UnitCreateParams) =>
    request<null>({
      url: '/wms/units',
      method: 'POST',
      data
    }),

  // 更新单位
  save: (data: Api.Wms.UnitUpdateParams) =>
    request<null>({
      url: `/wms/units/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除单位
  del: (id: number) =>
    request<null>({
      url: `/wms/units/${id}`,
      method: 'DELETE'
    }),

  // 批量删除单位
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/units',
      method: 'DELETE',
      data: { ids }
    })
};
