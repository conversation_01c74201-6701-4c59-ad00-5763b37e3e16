<script setup lang="ts">
import { $t } from '@/locales';

defineOptions({
  name: 'TableHeaderOperation'
});

interface Props {
  disabledAdd?: boolean;
  disabledDelete?: boolean;
  loading?: boolean;
}

defineProps<Props>();

interface Emits {
  (e: 'add'): void;
  (e: 'delete'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<NaiveUI.TableColumnCheck[]>('columns', {
  default: () => []
});

function add() {
  emit('add');
}

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}
</script>

<template>
  <div class="flex flex-wrap items-center justify-between gap-y-8px lt-sm:w-200px">
    <div class="flex flex-1 items-center gap-x-8px">
      <slot name="default">
        <NButton v-if="!disabledAdd" type="primary" @click="add">
          <template #icon>
            <icon-ph-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
        <NPopconfirm @positive-click="batchDelete">
          <template #trigger>
            <NButton ghost type="error" :disabled="disabledDelete">
              <template #icon>
                <icon-ph-trash class="text-icon" />
              </template>
              {{ $t('common.batchDelete') }}
            </NButton>
          </template>
          {{ $t('common.confirmDelete') }}
        </NPopconfirm>
      </slot>
      <slot name="prefix"></slot>
    </div>
    <div class="flex flex-1 items-center justify-end gap-x-8px lt-sm:hidden">
      <slot name="suffix"></slot>
      <NButton @click="refresh">
        <template #icon>
          <icon-ph-arrow-clockwise class="text-icon" :class="{ 'animate-spin': loading }" />
        </template>
      </NButton>
      <TableColumnSetting v-model:columns="columns" />
    </div>
  </div>
</template>

<style scoped></style>
