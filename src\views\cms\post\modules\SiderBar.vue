<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import type { TreeOption } from 'naive-ui';

interface Emits {
  (e: 'search'): void;
}
const emit = defineEmits<Emits>();

const metaId = defineModel<number | null | undefined>('value', { required: true });

defineProps<{
  metaOptions?: TreeOption[];
}>();

const pattern = ref('');

const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      metaId.value = metaId.value === (option.id as number) ? undefined : (option.id as number);
      emit('search');
    }
  };
};

const router = useRouter();
function handleMeta() {
  router.push('/cms/meta');
}
</script>

<template>
  <NCard title="所属栏目" :bordered="false" size="small" class="h-full">
    <template #header-extra>
      <NButton :focusable="false" quaternary @click="handleMeta">
        <icon-ph-sliders-horizontal class="text-icon" />
      </NButton>
    </template>
    <NInput v-model:value="pattern" placeholder="搜索" />
    <NScrollbar class="m-t-12px">
      <NTree
        :show-irrelevant-nodes="false"
        :pattern="pattern"
        :data="metaOptions"
        key-field="id"
        label-field="name"
        accordion
        block-line
        :node-props="nodeProps"
      />
    </NScrollbar>
  </NCard>
</template>

<style lang="scss" scoped>
:deep(.n-card__content) {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
}
:deep(.n-tree) {
  .n-tree-node {
    padding: 8px 16px;
    &.n-tree-node--selected {
      color: rgb(var(--primary-color));
      background-color: rgb(var(--primary-500-color) / 0.1);
    }
  }
}
</style>
