import { request } from '@/service/request';

export const dictApi = {
  // 获取字典列表
  list: (params: Api.System.DictSearchParams) =>
    request<Api.System.DictList>({
      url: '/system/dicts',
      params
    }),

  // 创建字典
  add: (data: Api.System.DictCreateParams) =>
    request<null>({
      url: '/system/dicts',
      method: 'POST',
      data
    }),

  // 更新字典
  save: (data: Api.System.DictUpdateParams) =>
    request<null>({
      url: `/system/dicts/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除字典
  del: (id: number) =>
    request<null>({
      url: `/system/dicts/${id}`,
      method: 'DELETE'
    }),

  // 批量删除角色
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/dicts',
      method: 'DELETE',
      data: { ids }
    })
};
