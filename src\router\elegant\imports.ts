/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  cms_meta: () => import("@/views/cms/meta/index.vue"),
  cms_post: () => import("@/views/cms/post/index.vue"),
  home: () => import("@/views/home/<USER>"),
  system_api: () => import("@/views/system/api/index.vue"),
  system_config: () => import("@/views/system/config/index.vue"),
  system_dept: () => import("@/views/system/dept/index.vue"),
  system_dict: () => import("@/views/system/dict/index.vue"),
  system_log: () => import("@/views/system/log/index.vue"),
  system_menu: () => import("@/views/system/menu/index.vue"),
  system_role: () => import("@/views/system/role/index.vue"),
  system_tenant: () => import("@/views/system/tenant/index.vue"),
  system_user: () => import("@/views/system/user/index.vue"),
  wms_area: () => import("@/views/wms/area/index.vue"),
  wms_bi: () => import("@/views/wms/bi/index.vue"),
  wms_check: () => import("@/views/wms/check/index.vue"),
  wms_deliver: () => import("@/views/wms/deliver/index.vue"),
  wms_item: () => import("@/views/wms/item/index.vue"),
  wms_item_modules_item: () => import("@/views/wms/item/modules/item/index.vue"),
  wms_item_modules_sku: () => import("@/views/wms/item/modules/sku/index.vue"),
  wms_log: () => import("@/views/wms/log/index.vue"),
  wms_partner: () => import("@/views/wms/partner/index.vue"),
  wms_receive: () => import("@/views/wms/receive/index.vue"),
  wms_staff: () => import("@/views/wms/staff/index.vue"),
  wms_stock: () => import("@/views/wms/stock/index.vue"),
  wms_transfer: () => import("@/views/wms/transfer/index.vue"),
};
