import { request } from '@/service/request';

export const apiApi = {
  // 获取接口列表
  list: (params: Api.System.ApiSearchParams) =>
    request<Api.System.ApiList>({
      url: '/system/apis',
      params
    }),

  // 创建接口
  add: (data: Api.System.ApiCreateParams) =>
    request<null>({
      url: '/system/apis',
      method: 'POST',
      data
    }),

  // 更新接口
  save: (data: Api.System.ApiUpdateParams) =>
    request<null>({
      url: `/system/apis/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除接口
  del: (id: number) =>
    request<null>({
      url: `/system/apis/${id}`,
      method: 'DELETE'
    }),

  // 批量删除接口
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/apis',
      method: 'DELETE',
      data: { ids }
    }),

  // 重置接口
  refresh: () => request<null>({ url: '/system/apis/refresh' })
};
