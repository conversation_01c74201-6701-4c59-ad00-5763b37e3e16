import { request } from '@/service/request';

export const itemApi = {
  // 获取物料列表
  list: (params: Api.Wms.ItemSearchParams) =>
    request<Api.Wms.ItemList>({
      url: '/wms/items',
      params
    }),

  // 创建物料
  add: (data: Api.Wms.ItemCreateParams) =>
    request<null>({
      url: '/wms/items',
      method: 'POST',
      data
    }),

  // 更新物料
  save: (data: Api.Wms.ItemUpdateParams) =>
    request<null>({
      url: `/wms/items/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除物料
  del: (id: number) =>
    request<null>({
      url: `/wms/items/${id}`,
      method: 'DELETE'
    }),

  // 批量删除物料
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/items',
      method: 'DELETE',
      data: { ids }
    })
};
