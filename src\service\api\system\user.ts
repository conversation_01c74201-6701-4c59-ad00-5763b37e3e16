import { request } from '@/service/request';

export const userApi = {
  // 获取用户列表
  list: (params: Api.System.UserSearchParams) =>
    request<Api.System.UserList>({
      url: '/system/users',
      params
    }),

  // 创建用户
  add: (data: Api.System.UserCreateParams) =>
    request<null>({
      url: '/system/users',
      method: 'POST',
      data
    }),

  // 更新用户
  save: (data: Api.System.UserUpdateParams) =>
    request<null>({
      url: `/system/users/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除用户
  del: (id: number) =>
    request<null>({
      url: `/system/users/${id}`,
      method: 'DELETE'
    }),

  // 批量删除用户
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/users',
      method: 'DELETE',
      data: { ids }
    }),

  // 重置密码
  password: (data: Api.System.UserPasswordParams) =>
    request<null>({
      url: `/system/users/${data.id}`,
      method: 'PATCH',
      data
    })
};
