import { request } from '../../request';

export const uploadApi = {
  // 上传图片
  image: (data: FormData, onProgress?: ({ percent }: { percent: number }) => void) =>
    request<{ fileName: string }>({
      url: '/upload/image',
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data,
      timeout: 1 * 60 * 1000,
      onUploadProgress: progressEvent =>
        onProgress &&
        onProgress({
          percent: Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1))
        })
    }),

  // 上传文件
  file: (data: FormData, onProgress?: ({ percent }: { percent: number }) => void) =>
    request<{ fileName: string }>({
      url: '/upload/file',
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data,
      timeout: 5 * 60 * 1000,
      onUploadProgress: progressEvent =>
        onProgress &&
        onProgress({
          percent: Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1))
        })
    }),

  // 删除图片
  remove: (fileName: string) =>
    request<null>({
      url: '/upload/remove',
      method: 'DELETE',
      data: { fileName }
    })
};
