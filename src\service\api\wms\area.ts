import { request } from '@/service/request';

export const areaApi = {
  // 获取仓库列表
  list: (params: Api.Wms.AreaSearchParams) =>
    request<Api.Wms.AreaList>({
      url: '/wms/areas',
      params
    }),

  // 创建仓库
  add: (data: Api.Wms.AreaCreateParams) =>
    request<null>({
      url: '/wms/areas',
      method: 'POST',
      data
    }),

  // 更新仓库
  save: (data: Api.Wms.AreaUpdateParams) =>
    request<null>({
      url: `/wms/areas/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除仓库
  del: (id: number) =>
    request<null>({
      url: `/wms/areas/${id}`,
      method: 'DELETE'
    }),

  // 批量删除仓库
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/areas',
      method: 'DELETE',
      data: { ids }
    })
};
