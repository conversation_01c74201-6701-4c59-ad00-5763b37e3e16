// @unocss-include
import { getRgb } from '@sa/color';
import { DARK_CLASS } from '@/constants/app';
import { localStg } from '@/utils/storage';
import { toggleHtmlClass } from '@/utils/common';

export function setupLoading() {
  const themeColor = localStg.get('themeColor') || '#646cff';
  const darkMode = localStg.get('darkMode') || false;
  const { r, g, b } = getRgb(themeColor);

  const primaryColor = `--primary-color: ${r} ${g} ${b}`;

  if (darkMode) {
    toggleHtmlClass(DARK_CLASS).add();
  }

  const loadingClasses = [
    'left-0 top-0 opacity-25',
    'left-0 bottom-0 opacity-50',
    'right-0 top-0 opacity-75',
    'right-0 bottom-0'
  ];

  const dot = loadingClasses
    .map(item => {
      return `<div class="absolute w-8px h-8px bg-primary rounded-8px ${item}"></div>`;
    })
    .join('\n');

  const loading = `
<div class="fixed-center flex-col bg-layout" style="${primaryColor}">
  <div class="w-24px h-24px">
    <div class="relative h-full animate-spin">
      ${dot}
    </div>
  </div>
</div>`;

  const app = document.getElementById('app');

  if (app) {
    app.innerHTML = loading;
  }
}
