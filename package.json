{"name": "soybean-admin", "type": "module", "version": "1.3.13", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.20.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode test", "build:prod": "vite build --mode prod", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:mock": "vite --mode mock", "gen-route": "sa gen-route", "lint": "eslint . --fix", "mock": "pnpm --filter mock run dev", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.3.0", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@unocss/reset": "66.1.0-beta.11", "@vant/area-data": "^2.0.0", "@vueuse/core": "13.0.0", "@wangeditor/editor": "^5.1.1", "@wangeditor/editor-for-vue": "^5.1.12", "clipboard": "2.0.11", "compressorjs": "^1.2.1", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "5.6.0", "json5": "2.2.3", "lodash-es": "^4.17.21", "naive-ui": "2.41.0", "nanoid": "5.1.5", "nprogress": "0.2.0", "pinia": "3.0.1", "tailwind-merge": "3.0.2", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "11.1.2", "vue-router": "4.5.0"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.318", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.6.0", "@types/lodash-es": "^4.17.12", "@types/node": "22.13.10", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-typography": "66.1.0-beta.11", "@unocss/preset-uno": "66.0.0", "@unocss/transformer-directives": "66.0.0", "@unocss/transformer-variant-group": "66.0.0", "@unocss/vite": "66.0.0", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "consola": "3.4.2", "eslint": "9.22.0", "eslint-plugin-vue": "10.0.0", "kolorist": "1.8.0", "lint-staged": "15.5.0", "sass": "1.86.0", "simple-git-hooks": "2.11.1", "tsx": "4.19.3", "typescript": "5.8.2", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.4.1", "vite": "6.2.2", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.2", "vue-eslint-parser": "10.1.1", "vue-tsc": "2.2.8"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://admin.soybeanjs.cn"}