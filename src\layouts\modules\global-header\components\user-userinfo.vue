<script setup lang="ts">
import { ref, watch } from 'vue';
import { authApi } from '@/service/api/base';
import { useAuthStore } from '@/store/modules/auth';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

const authStore = useAuthStore();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { patternRules } = useFormRules();

type Model = Pick<Api.Auth.UpdateUserInfoParams, 'nickname' | 'avatar' | 'email' | 'phone' | 'gender'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    nickname: authStore.userInfo?.nickname || '',
    avatar: authStore.userInfo?.avatar || '',
    email: authStore.userInfo?.email || '',
    phone: authStore.userInfo?.phone || '',
    gender: authStore.userInfo?.gender || 0
  };
}

type RuleKey = Extract<keyof Model, 'email' | 'phone'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  phone: patternRules.phone,
  email: patternRules.email
};

function handleInitModel() {
  model.value = createDefaultModel();
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = await authApi.updateUserInfo(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success('更新成功');
    closeDrawer();
    authStore.initUserInfo();
  } else {
    window.$message?.error('更新失败');
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" title="个人设置" preset="card" class="w-500px">
    <NForm ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
      <NFormItem label="昵称" path="nickname">
        <NInput v-model:value="model.nickname" placeholder="请输入昵称" clearable />
      </NFormItem>
      <NFormItem label="手机号" path="phone">
        <NInput v-model:value="model.phone" placeholder="请输入手机号" clearable />
      </NFormItem>
      <NFormItem label="邮箱" path="email">
        <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
      </NFormItem>
      <NFormItem label="性别" path="gender">
        <NRadioGroup v-model:value="model.gender">
          <NSpace>
            <NRadio
              v-for="item in useDict('number').items('Gender')"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </NSpace>
        </NRadioGroup>
      </NFormItem>
      <NFormItem v-if="visible" label="头像" path="avatar">
        <UploadCover v-model:value="model.avatar" :options="{ maxWidth: 200, maxHeight: 200 }" />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="visible = false">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
