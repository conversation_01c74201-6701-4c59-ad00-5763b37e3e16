import { request } from '@/service/request';

export const tenantApi = {
  // 获取租户列表
  list: (params: Api.System.TenantSearchParams) =>
    request<Api.System.TenantList>({
      url: '/system/tenants',
      params
    }),

  // 创建租户
  add: (data: Api.System.TenantCreateParams) =>
    request<null>({
      url: '/system/tenants',
      method: 'POST',
      data
    }),

  // 更新租户
  save: (data: Api.System.TenantUpdateParams) =>
    request<null>({
      url: `/system/tenants/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除租户
  del: (id: number) =>
    request<null>({
      url: `/system/tenants/${id}`,
      method: 'DELETE'
    }),

  // 批量删除租户
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/tenants',
      method: 'DELETE',
      data: { ids }
    })
};
