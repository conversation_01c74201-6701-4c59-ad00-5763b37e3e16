<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { menuApi } from '@/service/api/system';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType | 'addChild';
  /** the edit row data */
  rowData?: Api.System.Menu | null;
  /** the parent options */
  menus?: Api.System.Menu[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType | 'addChild', string> = {
    add: '新增菜单',
    edit: '编辑菜单',
    addChild: '新增子菜单'
  };
  return titles[props.operateType];
});

// 递归过滤parentOptions，只保留menuType=2的选项
const parentOptions = computed(() => {
  if (!props.menus) return [];

  const filterMenuType = (menus: Api.System.Menu[]): Api.System.Menu[] => {
    return menus
      .filter(menu => menu.menuType === 1)
      .map(menu => {
        const newMenu = { ...menu };
        if (newMenu.children && newMenu.children.length > 0) {
          newMenu.children = filterMenuType(newMenu.children);
        }
        return newMenu;
      });
  };

  return filterMenuType(props.menus);
});

type Model = Pick<
  Api.System.Menu,
  | 'menuType'
  | 'menuName'
  | 'routeName'
  | 'routePath'
  | 'layout'
  | 'component'
  | 'icon'
  | 'order'
  | 'href'
  | 'constant'
  | 'hideInMenu'
  | 'keepAlive'
  | 'multiTab'
  | 'status'
  | 'parentId'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    menuType: 1,
    menuName: '',
    routeName: '',
    routePath: '',
    layout: 'default',
    component: '',
    icon: import.meta.env.VITE_MENU_ICON,
    order: 0,
    href: null,
    constant: false,
    hideInMenu: false,
    keepAlive: false,
    multiTab: false,
    status: true,
    parentId: 0
  };
}

type RuleKey = Extract<keyof Model, 'menuType' | 'menuName' | 'routeName' | 'routePath' | 'layout'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  menuType: defaultRequiredRule,
  menuName: defaultRequiredRule,
  routeName: defaultRequiredRule,
  routePath: defaultRequiredRule,
  layout: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }

  // 新增子栏目
  if (props.operateType === 'addChild' && props.rowData) {
    Object.assign(model.value, { parentId: props.rowData.id });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await menuApi.save(model.value) : await menuApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="父级菜单" path="parentId">
          <NTreeSelect
            v-model:value="model.parentId"
            :options="parentOptions"
            key-field="id"
            label-field="menuName"
            clearable
          />
        </NFormItem>
        <NFormItem label="菜单类型" path="menuType">
          <NSelect v-model:value="model.menuType" :options="useDict('number').items('MenuType')" clearable />
        </NFormItem>
        <NFormItem label="菜单名称" path="menuName">
          <NInput v-model:value="model.menuName" placeholder="请输入菜单名称" clearable />
        </NFormItem>
        <NFormItem label="路由名称" path="routeName">
          <NInput v-model:value="model.routeName" placeholder="请输入路由名称" clearable />
        </NFormItem>
        <NFormItem label="路由路径" path="routePath">
          <NInput v-model:value="model.routePath" placeholder="请输入路由路径" clearable />
        </NFormItem>
        <NFormItem label="页面布局" path="layout">
          <NSelect v-model:value="model.layout" :options="useDict().items('MenuLayout')" clearable />
        </NFormItem>
        <NFormItem v-if="model.menuType === 2" label="页面组件" path="component">
          <NInput v-model:value="model.component" placeholder="请输入组件路径" clearable />
        </NFormItem>
        <NFormItem label="菜单图标" path="icon">
          <NInput v-model:value="model.icon" placeholder="请输入图标" clearable>
            <template #suffix>
              <a href="https://icones.js.org/collection/ph" target="_blank" rel="noopener noreferrer">
                <SvgIcon v-if="model.icon" :icon="model.icon" class="text-icon" />
              </a>
            </template>
          </NInput>
        </NFormItem>
        <NFormItem label="外链" path="href">
          <NInput v-model:value="model.href" placeholder="请输入链接" clearable />
        </NFormItem>
        <NGrid responsive="screen" item-responsive>
          <NFormItemGi span="24 m:12" label="常量" path="constant">
            <NSwitch v-model:value="model.constant" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="隐藏" path="hideInMenu">
            <NSwitch v-model:value="model.hideInMenu" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="缓存" path="keepAlive">
            <NSwitch v-model:value="model.keepAlive" />
          </NFormItemGi>
          <NFormItemGi span="24 m:12" label="多标签" path="multiTab">
            <NSwitch v-model:value="model.multiTab" />
          </NFormItemGi>
        </NGrid>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" :min="0" :precision="0" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
