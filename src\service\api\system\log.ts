import { request } from '@/service/request';

export const logApi = {
  // 获取日志列表
  list: (params: Api.System.LogSearchParams) =>
    request<Api.System.LogList>({
      url: '/system/logs',
      params
    }),

  // 删除日志
  del: (id: number) =>
    request<null>({
      url: `/system/logs/${id}`,
      method: 'DELETE'
    }),

  // 批量删除日志
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/logs',
      method: 'DELETE',
      data: { ids }
    })
};
