[{"id": 1, "path": "/auth/constant_routes", "method": "GET", "tags": ["认证模块"], "summary": "获取系统常量路由", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 2, "path": "/auth/exist_route", "method": "GET", "tags": ["认证模块"], "summary": "检查指定路由名称是否存在", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 3, "path": "/auth/login", "method": "POST", "tags": ["认证模块"], "summary": "使用用户名密码登录系统", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 4, "path": "/auth/logout", "method": "POST", "tags": ["认证模块"], "summary": "注销登录", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 5, "path": "/auth/password", "method": "POST", "tags": ["认证模块"], "summary": "修改当前登录用户的密码", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 6, "path": "/auth/refresh", "method": "POST", "tags": ["认证模块"], "summary": "使用 refreshToken 刷新认证令牌", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 7, "path": "/auth/user_routes", "method": "GET", "tags": ["认证模块"], "summary": "获取当前登录用户的菜单路由", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 8, "path": "/auth/userinfo", "method": "POST", "tags": ["认证模块"], "summary": "更新当前登录用户的用户信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 9, "path": "/auth/userinfo", "method": "GET", "tags": ["认证模块"], "summary": "获取当前登录用户的用户信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 10, "path": "/cms/metas", "method": "GET", "tags": ["内容模块", "栏目管理"], "summary": "分页获取栏目列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 11, "path": "/cms/metas", "method": "DELETE", "tags": ["内容模块", "栏目管理"], "summary": "批量删除指定IDs的栏目", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 12, "path": "/cms/metas", "method": "POST", "tags": ["内容模块", "栏目管理"], "summary": "创建新的栏目记录", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 13, "path": "/cms/metas/{id}", "method": "DELETE", "tags": ["内容模块", "栏目管理"], "summary": "删除指定ID的栏目", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 14, "path": "/cms/metas/{id}", "method": "PATCH", "tags": ["内容模块", "栏目管理"], "summary": "更新指定ID的栏目信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 15, "path": "/cms/metas/{id}", "method": "GET", "tags": ["内容模块", "栏目管理"], "summary": "获取指定ID的栏目信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 16, "path": "/cms/posts", "method": "DELETE", "tags": ["内容模块", "文章管理"], "summary": "批量删除指定IDs的文章", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 17, "path": "/cms/posts", "method": "POST", "tags": ["内容模块", "文章管理"], "summary": "创建新的文章记录", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 18, "path": "/cms/posts", "method": "GET", "tags": ["内容模块", "文章管理"], "summary": "分页获取文章列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 19, "path": "/cms/posts/{id}", "method": "GET", "tags": ["内容模块", "文章管理"], "summary": "获取指定ID的文章信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 20, "path": "/cms/posts/{id}", "method": "DELETE", "tags": ["内容模块", "文章管理"], "summary": "删除指定ID的文章", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 21, "path": "/cms/posts/{id}", "method": "PATCH", "tags": ["内容模块", "文章管理"], "summary": "更新指定ID的文章信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 22, "path": "/system/apis", "method": "DELETE", "tags": ["系统模块", "接口管理"], "summary": "批量删除指定IDs的接口", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 23, "path": "/system/apis", "method": "POST", "tags": ["系统模块", "接口管理"], "summary": "创建新的接口记录", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 24, "path": "/system/apis", "method": "GET", "tags": ["系统模块", "接口管理"], "summary": "分页获取接口列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 25, "path": "/system/apis/refresh", "method": "GET", "tags": ["系统模块", "接口管理"], "summary": "重置所有接口信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 26, "path": "/system/apis/{id}", "method": "GET", "tags": ["系统模块", "接口管理"], "summary": "获取指定ID的接口信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 27, "path": "/system/apis/{id}", "method": "PATCH", "tags": ["系统模块", "接口管理"], "summary": "更新指定ID的接口信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 28, "path": "/system/apis/{id}", "method": "DELETE", "tags": ["系统模块", "接口管理"], "summary": "删除指定ID的接口", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 29, "path": "/system/configs", "method": "DELETE", "tags": ["系统模块", "配置管理"], "summary": "批量删除指定IDs的配置", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 30, "path": "/system/configs", "method": "GET", "tags": ["系统模块", "配置管理"], "summary": "分页获取配置列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 31, "path": "/system/configs", "method": "POST", "tags": ["系统模块", "配置管理"], "summary": "创建新的配置记录", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 32, "path": "/system/configs/{id}", "method": "DELETE", "tags": ["系统模块", "配置管理"], "summary": "删除指定ID的配置", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 33, "path": "/system/configs/{id}", "method": "PATCH", "tags": ["系统模块", "配置管理"], "summary": "更新指定ID的配置信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 34, "path": "/system/configs/{id}", "method": "GET", "tags": ["系统模块", "配置管理"], "summary": "获取指定ID的配置信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 35, "path": "/system/depts", "method": "POST", "tags": ["系统模块", "部门管理"], "summary": "创建新的部门", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 36, "path": "/system/depts", "method": "DELETE", "tags": ["系统模块", "部门管理"], "summary": "批量删除指定IDs的部门", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 37, "path": "/system/depts", "method": "GET", "tags": ["系统模块", "部门管理"], "summary": "分页获取部门列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 38, "path": "/system/depts/{id}", "method": "GET", "tags": ["系统模块", "部门管理"], "summary": "获取指定ID的部门信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 39, "path": "/system/depts/{id}", "method": "DELETE", "tags": ["系统模块", "部门管理"], "summary": "删除指定ID的部门", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 40, "path": "/system/depts/{id}", "method": "PATCH", "tags": ["系统模块", "部门管理"], "summary": "更新指定ID的部门信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 41, "path": "/system/dicts", "method": "GET", "tags": ["系统模块", "字典管理"], "summary": "分页获取字典列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 42, "path": "/system/dicts", "method": "POST", "tags": ["系统模块", "字典管理"], "summary": "创建新的字典记录", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 43, "path": "/system/dicts", "method": "DELETE", "tags": ["系统模块", "字典管理"], "summary": "批量删除指定IDs的字典", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 44, "path": "/system/dicts/{id}", "method": "PATCH", "tags": ["系统模块", "字典管理"], "summary": "更新指定ID的字典信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 45, "path": "/system/dicts/{id}", "method": "DELETE", "tags": ["系统模块", "字典管理"], "summary": "删除指定ID的字典", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 46, "path": "/system/dicts/{id}", "method": "GET", "tags": ["系统模块", "字典管理"], "summary": "获取指定ID的字典信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 47, "path": "/system/logs", "method": "GET", "tags": ["系统模块", "日志管理"], "summary": "分页获取日志列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 48, "path": "/system/logs", "method": "DELETE", "tags": ["系统模块", "日志管理"], "summary": "批量删除指定IDs的日志", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 49, "path": "/system/logs/{id}", "method": "DELETE", "tags": ["系统模块", "日志管理"], "summary": "删除指定ID的日志", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 50, "path": "/system/logs/{id}", "method": "GET", "tags": ["系统模块", "日志管理"], "summary": "获取指定ID的日志信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 51, "path": "/system/menus", "method": "GET", "tags": ["系统模块", "菜单管理"], "summary": "分页获取菜单列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 52, "path": "/system/menus", "method": "POST", "tags": ["系统模块", "菜单管理"], "summary": "创建新的菜单", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 53, "path": "/system/menus", "method": "DELETE", "tags": ["系统模块", "菜单管理"], "summary": "批量删除指定IDs的菜单", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 54, "path": "/system/menus/{id}", "method": "PATCH", "tags": ["系统模块", "菜单管理"], "summary": "更新指定ID的菜单信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 55, "path": "/system/menus/{id}", "method": "DELETE", "tags": ["系统模块", "菜单管理"], "summary": "删除指定ID的菜单", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 56, "path": "/system/menus/{id}", "method": "GET", "tags": ["系统模块", "菜单管理"], "summary": "获取指定ID的菜单信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 57, "path": "/system/roles", "method": "GET", "tags": ["系统模块", "角色管理"], "summary": "分页获取角色列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 58, "path": "/system/roles", "method": "DELETE", "tags": ["系统模块", "角色管理"], "summary": "批量删除指定IDs的角色", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 59, "path": "/system/roles", "method": "POST", "tags": ["系统模块", "角色管理"], "summary": "创建新的角色", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 60, "path": "/system/roles/{id}", "method": "GET", "tags": ["系统模块", "角色管理"], "summary": "获取指定ID的角色信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 61, "path": "/system/roles/{id}", "method": "PATCH", "tags": ["系统模块", "角色管理"], "summary": "更新指定ID的角色信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 62, "path": "/system/roles/{id}", "method": "DELETE", "tags": ["系统模块", "角色管理"], "summary": "删除指定ID的角色", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 63, "path": "/system/users", "method": "DELETE", "tags": ["系统模块", "用户管理"], "summary": "批量删除指定IDs的用户", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 64, "path": "/system/users", "method": "GET", "tags": ["系统模块", "用户管理"], "summary": "分页获取用户列表", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 65, "path": "/system/users", "method": "POST", "tags": ["系统模块", "用户管理"], "summary": "创建新的用户", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 66, "path": "/system/users/{id}", "method": "PATCH", "tags": ["系统模块", "用户管理"], "summary": "更新指定ID的用户信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 67, "path": "/system/users/{id}", "method": "DELETE", "tags": ["系统模块", "用户管理"], "summary": "删除指定ID的用户", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 68, "path": "/system/users/{id}", "method": "GET", "tags": ["系统模块", "用户管理"], "summary": "获取指定ID的用户信息", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 69, "path": "/upload/file", "method": "POST", "tags": ["上传模块"], "summary": "上传文件到MinIO服务器", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 70, "path": "/upload/image", "method": "POST", "tags": ["上传模块"], "summary": "上传图片到MinIO服务器", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}, {"id": 71, "path": "/upload/remove", "method": "DELETE", "tags": ["上传模块"], "summary": "从MinIO服务器删除资源", "status": true, "createdAt": "2025-07-03T11:28:48+08:00", "updatedAt": "2025-07-03T11:28:48+08:00"}]