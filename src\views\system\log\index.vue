<script setup lang="tsx">
import { N<PERSON><PERSON><PERSON>, NPopconfirm, NTag } from 'naive-ui';
import dayjs from 'dayjs';
import { logApi } from '@/service/api/system';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import SearchBar from './modules/SearchBar.vue';

const appStore = useAppStore();

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination, searchParams, resetSearchParams } =
  useTable({
    apiFn: logApi.list,
    showTotal: true,
    apiParams: {
      _page: 1,
      _limit: 10,
      _sort: 'id',
      _order: 'desc',
      userId: null,
      path: null,
      method: null,
      code: null,
      clientIp: null
    },
    columns: () => [
      {
        type: 'selection',
        align: 'center',
        width: 48
      },
      {
        key: 'id',
        title: '#',
        align: 'center',
        width: 64
      },
      {
        key: 'path',
        title: '路径',
        align: 'left'
      },
      {
        key: 'method',
        title: '请求方法',
        align: 'left',
        render: row => {
          if (row.method === null) return null;

          const option = useDict().item('HttpMethod', row.method);
          if (!option) return null;
          return <NTag type={option.type}>{option.label}</NTag>;
        }
      },
      {
        key: 'code',
        title: '状态码',
        align: 'left'
      },
      {
        key: 'clientIp',
        title: '客户端IP',
        align: 'left'
      },
      {
        key: 'createdAt',
        title: '请求时间',
        align: 'left',
        render: row => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
      },
      {
        key: 'operate',
        title: '操作',
        align: 'center',
        fixed: 'right',
        width: 150,
        render: row => (
          <div class="flex flex-center gap-12px">
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => '确定删除吗？',
                trigger: () => (
                  <NButton type="error" text size="small">
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await logApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await logApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          disabled-add
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
