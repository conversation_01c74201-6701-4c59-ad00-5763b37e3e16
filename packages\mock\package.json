{"name": "mock", "version": "1.0.0", "packageManager": "pnpm@10.4.1", "description": "JSON Server with JWT authentication", "author": "", "license": "ISC", "keywords": ["json-server", "mock", "api", "rest"], "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js"}, "dependencies": {"cors": "^2.8.5", "json-server": "^0.17.4", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.1.9"}}