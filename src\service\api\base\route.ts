import { request } from '../../request';

export const routeApi = {
  // 获取常量路由
  getConstantRoutes: () =>
    request<Api.Route.MenuRoute[]>({
      url: '/auth/constant_routes'
    }),

  // 获取用户路由
  getUserRoutes: () =>
    request<Api.Route.MenuRoute[]>({
      url: '/auth/user_routes'
    }),

  // 判断路由是否存在
  isRouteExist: (routeName: string) =>
    request<boolean>({
      url: '/auth/exist_route',
      params: { routeName }
    })
};
