<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512"><defs><linearGradient id="a" x1="138.5" x2="224.2" y1="5.1" y2="153.5" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#d4d7dd"/><stop offset=".5" stop-color="#d4d7dd"/><stop offset="1" stop-color="#bec1c6"/></linearGradient><linearGradient xlink:href="#a" id="b" x1="77.7" x2="169" y1="96.2" y2="254.4"/><linearGradient id="c" x1="219.1" x2="240.9" y1="155.2" y2="192.8" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#86c3db"/><stop offset=".5" stop-color="#86c3db"/><stop offset="1" stop-color="#5eafcf"/></linearGradient><linearGradient xlink:href="#c" id="d" x1="363.1" x2="384.9" y1="257.2" y2="294.8"/><linearGradient xlink:href="#c" id="e" x1="133.1" x2="154.9" y1="315.2" y2="352.8"/><symbol id="f" viewBox="0 0 348 240"><path fill="none" stroke="url(#a)" stroke-dasharray="148" stroke-linecap="round" stroke-miterlimit="10" stroke-width="24" d="M267.2 24.3A40 40 0 11296 92H12"><animate attributeName="stroke-dashoffset" dur="6s" repeatCount="indefinite" values="0; 2960"/></path><path fill="none" stroke="url(#b)" stroke-dasharray="110" stroke-linecap="round" stroke-miterlimit="10" stroke-width="24" d="M151.2 215.7A40 40 0 10180 148H12"><animate attributeName="stroke-dashoffset" dur="6s" repeatCount="indefinite" values="0; 1540"/></path></symbol></defs><use xlink:href="#f" width="348" height="240" transform="translate(82 136)"/><path fill="url(#c)" stroke="#86c3db" stroke-miterlimit="10" d="m249.6 180.7-5.8-3.4a14.3 14.3 0 000-6.6l5.8-3.4a4.1 4.1 0 001.4-5.5 4 4 0 00-5.5-1.5l-5.8 3.3a14.2 14.2 0 00-2.6-2 14.6 14.6 0 00-3-1.3v-6.7a4 4 0 10-8.1 0v6.7a14.2 14.2 0 00-5.7 3.3l-5.8-3.3a4 4 0 00-5.5 1.5 4.1 4.1 0 001.5 5.5l5.8 3.4a14.3 14.3 0 000 6.6l-5.8 3.4a4.1 4.1 0 00-1.5 5.5 4 4 0 005.5 1.5l5.8-3.3a14.2 14.2 0 002.6 2 13.8 13.8 0 003 1.3v6.7a4 4 0 108.1 0v-6.7a14.1 14.1 0 005.7-3.3l5.8 3.3a4 4 0 005.5-1.5 4.1 4.1 0 00-1.4-5.5Zm-22.6-1.4a6.2 6.2 0 01-2.2-8.4 6 6 0 015.2-3 6 6 0 013 .8 6.2 6.2 0 012.3 8.4 6 6 0 01-8.3 2.2Z" opacity="0"><animateTransform id="x1" additive="sum" attributeName="transform" begin=".5s; x1.end+1.33s" dur="1.67s" type="translate" values="-30 0; 30 0"/><animate attributeName="opacity" begin=".5s; x1.end+1.33s" dur="1.67s" keyTimes="0; .2; .8; 1" values="0; 1; 1; 0"/></path><path fill="url(#d)" stroke="#86c3db" stroke-miterlimit="10" d="m393.6 282.7-5.8-3.4a14.3 14.3 0 000-6.6l5.8-3.4a4.1 4.1 0 001.4-5.5 4 4 0 00-5.5-1.5l-5.8 3.3a14.2 14.2 0 00-2.6-2 14.6 14.6 0 00-3-1.3v-6.7a4 4 0 10-8.1 0v6.7a14.2 14.2 0 00-5.7 3.3l-5.8-3.3a4 4 0 00-5.5 1.5 4.1 4.1 0 001.5 5.5l5.8 3.4a14.3 14.3 0 000 6.6l-5.8 3.4a4.1 4.1 0 00-1.5 5.5 4 4 0 005.5 1.5l5.8-3.3a14.2 14.2 0 002.7 2 13.8 13.8 0 003 1.3v6.7a4 4 0 108 0v-6.7a14.1 14.1 0 005.7-3.3l5.8 3.3a4 4 0 005.5-1.5 4.1 4.1 0 00-1.4-5.5Zm-22.6-1.4a6.2 6.2 0 01-2.2-8.4 6 6 0 015.2-3 6 6 0 013 .8 6.2 6.2 0 012.3 8.4 6 6 0 01-8.3 2.2Z" opacity="0"><animateTransform id="x2" additive="sum" attributeName="transform" begin="1s; x2.end+1.33s" dur="1.67s" type="translate" values="-30 0; 30 0"/><animate attributeName="opacity" begin="1s; x2.end+1.33s" dur="1.67s" keyTimes="0; .2; .8; 1" values="0; 1; 1; 0"/></path><path fill="url(#e)" stroke="#86c3db" stroke-miterlimit="10" d="m163.6 340.7-5.8-3.4a14.3 14.3 0 000-6.6l5.8-3.4a4.1 4.1 0 001.4-5.5 4 4 0 00-5.5-1.5l-5.8 3.3a14.2 14.2 0 00-2.6-2 14.6 14.6 0 00-3-1.3v-6.7a4 4 0 10-8.1 0v6.7a14.2 14.2 0 00-5.7 3.3l-5.8-3.3a4 4 0 00-5.5 1.5 4.1 4.1 0 001.5 5.5l5.8 3.4a14.3 14.3 0 000 6.6l-5.8 3.4a4.1 4.1 0 00-1.5 5.5 4 4 0 005.5 1.5l5.8-3.3a14.2 14.2 0 002.6 2 13.8 13.8 0 003 1.3v6.7a4 4 0 108.1 0v-6.7a14.1 14.1 0 005.7-3.3l5.8 3.3a4 4 0 005.5-1.5 4.1 4.1 0 00-1.4-5.5Zm-22.6-1.4a6.2 6.2 0 01-2.2-8.4 6 6 0 015.2-3 6 6 0 013 .8 6.2 6.2 0 012.3 8.4 6 6 0 01-8.3 2.2Z" opacity="0"><animateTransform id="x3" additive="sum" attributeName="transform" begin="0s; x3.end+1.33s" dur="1.67s" type="translate" values="-30 0; 30 0"/><animate attributeName="opacity" begin="0s; x3.end+1.33s" dur="1.67s" keyTimes="0; .2; .8; 1" values="0; 1; 1; 0"/></path></svg>