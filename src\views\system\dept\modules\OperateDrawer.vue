<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { deptApi } from '@/service/api/system';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { disableTreeItem } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType | 'addChild';
  /** the edit row data */
  rowData?: Api.System.Dept | null;
  /** the tree data */
  treeData?: Api.System.Dept[] | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType | 'addChild', string> = {
    add: '新增部门',
    edit: '编辑部门',
    addChild: '新增子部门'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.System.Dept, 'name' | 'code' | 'status' | 'summary' | 'order' | 'parentId'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    status: true,
    summary: '',
    order: 0,
    parentId: 0
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }

  // 新增子部门
  if (props.operateType === 'addChild') {
    Object.assign(model.value, { parentId: props.rowData?.id });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await deptApi.save(model.value) : await deptApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const parentOptions = computed(() => {
  if (props.operateType === 'edit' || props.operateType === 'addChild') {
    const options = props.treeData;
    return props.operateType === 'edit' ? disableTreeItem(options, props.rowData?.id) : options;
  }
  return props.treeData;
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="编码" path="code">
          <NInput v-model:value="model.code" placeholder="请输入编码" clearable />
        </NFormItem>
        <NFormItem label="描述" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入描述" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" :min="0" :precision="0" clearable />
        </NFormItem>
        <NFormItem label="父级" path="parentId">
          <NTreeSelect
            v-model:value="model.parentId"
            :options="parentOptions"
            key-field="id"
            label-field="name"
            :disabled="operateType === 'addChild'"
            clearable
          />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
