import { request } from '../../request';

export const authApi = {
  // 登录
  login: (data: Api.Auth.LoginParams) =>
    request<Api.Auth.LoginToken>({
      url: '/auth/login',
      method: 'POST',
      data
    }),

  // 刷新token
  refreshToken: (refreshToken: string) =>
    request<Api.Auth.LoginToken>({
      url: '/auth/refresh',
      method: 'POST',
      data: { refreshToken }
    }),

  // 获取用户信息
  getUserInfo: () =>
    request<Api.Auth.UserInfo>({
      url: '/auth/userinfo'
    }),

  // 更新用户信息
  updateUserInfo: (data: Api.Auth.UpdateUserInfoParams) =>
    request<null>({
      url: '/auth/userinfo',
      method: 'POST',
      data
    }),

  // 修改密码
  updatePassword: (data: Api.Auth.UpdatePasswordParams) =>
    request<null>({
      url: '/auth/password',
      method: 'POST',
      data
    }),

  // 自定义后端错误码
  customBackendError: (params: Api.Auth.CustomBackendErrorParams) =>
    request({
      url: '/auth/error',
      params
    })
};
