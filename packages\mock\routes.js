const process = require('node:process');
const jwt = require('jsonwebtoken');

// JWT密钥
const JWT_SECRET_KEY = process.env.JWT_SECRET_KEY || 'your-secret-key';

// 自定义路由
const customRoutes = (server, db = null) => {
  // 批量删除
  server.delete('/api/:module/:source', (req, res) => {
    const source = req.params.source;
    if (db && db[source]) {
      req.body.ids.forEach(id => {
        db[source] = db[source].filter(item => item.id !== Number.parseInt(id, 10));
      });
    }

    res.jsonp({
      code: 200,
      message: '删除成功'
    });
  });

  // 登录授权
  server.post('/api/auth/login', (req, res) => {
    const { username, password } = req.body;

    const user = db.users.find(u => u.username === username && u.password === password);

    if (user) {
      // 生成token
      const token = jwt.sign(
        {
          id: user.id,
          username: user.username,
          name: user.name,
          roleIds: user.roleIds
        },
        JWT_SECRET_KEY,
        { expiresIn: '24h' }
      );
      // 生成refreshToken
      const refreshToken = jwt.sign(
        {
          id: user.id,
          username: user.username,
          name: user.name,
          roleIds: user.roleIds
        },
        JWT_SECRET_KEY,
        { expiresIn: '7d' }
      );

      res.jsonp({
        code: 200,
        data: { token, refreshToken },
        message: '登录成功'
      });
    } else {
      res.status(400).jsonp({
        code: 400,
        message: '用户名或密码错误'
      });
    }
  });

  // 注销登录
  server.post('/api/auth/logout', (_, res) => {
    res.jsonp({
      code: 200,
      message: '注销成功'
    });
  });

  // 获取用户信息
  server.get('/api/auth/userinfo', (req, res) => {
    const user = db.users.find(u => u.id === req.user.id);

    // 获取用户角色
    const roles = db.roles.filter(r => user.roleIds.includes(r.id));

    if (user) {
      // @密码脱敏
      const { password: _, ...safeUser } = user;

      res.jsonp({
        code: 200,
        data: { ...safeUser, roles },
        message: 'OK'
      });
    } else {
      res.status(400).jsonp({
        code: 400,
        message: '该用户不存在'
      });
    }
  });

  // 更新用户信息
  server.post('/api/auth/userinfo', (req, res) => {
    const user = db.users.find(u => u.id === req.user.id);
    if (user) {
      user.name = req.body.name;
      user.email = req.body.email;
      user.phone = req.body.phone;
      user.avatar = req.body.avatar;
      res.jsonp({
        code: 200,
        message: '更新成功'
      });
    } else {
      res.status(400).jsonp({
        code: 400,
        message: '该用户不存在'
      });
    }
  });

  // 修改密码
  server.post('/api/auth/password', (req, res) => {
    const user = db.users.find(u => u.id === req.user.id);
    if (user) {
      user.password = req.body.password;
      res.jsonp({
        code: 200,
        message: '修改成功'
      });
    } else {
      res.status(400).jsonp({
        code: 400,
        message: '该用户不存在'
      });
    }
  });

  // 上传图片
  server.post('/api/upload/image', (_, res) => {
    const data = { fileName: 'images/cover.jpg' };
    res.jsonp({
      code: 200,
      data,
      message: '上传成功'
    });
  });

  // 上传文件
  server.post('/api/upload/file', (_, res) => {
    const data = { fileName: 'files/cover.jpg' };
    res.jsonp({
      code: 200,
      data,
      message: '上传成功'
    });
  });

  // 删除文件
  server.delete('/api/upload/remove', (_, res) => {
    res.jsonp({
      code: 200,
      message: '删除成功'
    });
  });
};

// 验证JWT中间件
const authenticateJWT = (req, res, next) => {
  // 白名单路由
  const whiteList = ['/api/auth/login', '/api/auth/constant_routes'];
  if (whiteList.includes(req.path)) {
    next();
  } else if (!req.path.startsWith('/api')) {
    res.status(404).send();
  } else {
    // 获取token
    const token = req.headers.authorization?.split(' ')[1];

    // 验证token
    jwt.verify(token, JWT_SECRET_KEY, (err, user) => {
      if (err) {
        res.status(401).jsonp({
          code: 401,
          message: '登录无效或已过期'
        });
      } else {
        // 设置用户信息
        req.user = user;
        next();
      }
    });
  }
};

module.exports = {
  customRoutes,
  authenticateJWT
};
