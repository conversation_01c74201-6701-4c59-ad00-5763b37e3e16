import { useAuthStore } from '@/store/modules/auth';

export function useDict(type: string | undefined = undefined) {
  const authStore = useAuthStore();

  function items(code: string) {
    if (!authStore.dictList) return [];

    const dict = authStore.dictList?.find(res => res.code === code);

    if (dict) {
      return dict.options?.map((res: any) => {
        switch (type) {
          case 'number':
            res.value = Number(res.value);
            break;
          case 'bool':
            res.value = Boolean(res.value === '1' || res.value === 'true');
            break;
          default:
            break;
        }
        return res;
      });
    }
    return [];
  }

  function item(code: string, value: any) {
    return items(code)?.find(res => res.value === value);
  }

  return {
    items,
    item
  };
}
