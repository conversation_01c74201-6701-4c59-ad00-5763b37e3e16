<script lang="ts" setup>
defineOptions({
  name: 'TableQuickSearch',
  inheritAttrs: false
});

withDefaults(
  defineProps<{
    placeholder?: string;
  }>(),
  {
    placeholder: '请输入关键字'
  }
);

const value = defineModel<string | null | undefined>('value', { required: true });

interface Emits {
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

function refresh() {
  emit('refresh');
}
</script>

<template>
  <NInputGroup class="min-w-200px w-50%">
    <NInput v-model:value="value" v-bind="$attrs" :placeholder="placeholder" />
    <NButton @click="refresh">
      <template #icon>
        <icon-ph-magnifying-glass class="text-icon" />
      </template>
    </NButton>
  </NInputGroup>
</template>

<style scoped></style>
