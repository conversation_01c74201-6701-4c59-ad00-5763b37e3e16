import { request } from '@/service/request';

export const configApi = {
  // 获取配置列表
  list: (params: Api.System.ConfigSearchParams) =>
    request<Api.System.ConfigList>({
      url: '/system/configs',
      params
    }),

  // 创建配置
  add: (data: Api.System.ConfigCreateParams) =>
    request<null>({
      url: '/system/configs',
      method: 'POST',
      data
    }),

  // 更新配置
  save: (data: Api.System.ConfigUpdateParams) =>
    request<null>({
      url: `/system/configs/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除配置
  del: (id: number) =>
    request<null>({
      url: `/system/configs/${id}`,
      method: 'DELETE'
    }),

  // 批量删除配置
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/system/configs',
      method: 'DELETE',
      data: { ids }
    })
};
