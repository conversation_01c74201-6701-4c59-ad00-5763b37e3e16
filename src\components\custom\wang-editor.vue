<script lang="ts" setup>
import '@wangeditor/editor/dist/css/style.css';
import { onBeforeUnmount, shallowRef } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { uploadApi } from '@/service/api/base';
import { useCompressor } from '@/hooks/business/compressor';

defineOptions({
  name: 'WangEditor'
});

interface Props {
  height?: number;
}

defineProps<Props>();

type InsertFnType = (url: string, alt: string, href: string) => void;

const value = defineModel<string>('value', {
  default: ''
});

const mode = 'default';

const editorRef = shallowRef();

const toolbarConfig: any = { excludeKeys: ['uploadVideo'] };

const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {
    uploadImage: {
      // 服务端上传地址，根据实际业务改写
      // server: `${import.meta.env.VITE_API_BASE_URL}/UpLoadImage`,
      // 是否携带凭证
      // withCredentials: true,
      // 自定义请求头
      // headers: { Authorization },
      // form-data 的 fieldName，根据实际业务改写
      fieldName: 'file',
      // 单个文件的最大体积限制，默认为 10M
      maxFileSize: 10 * 1024 * 1024,
      // 小于该值就插入 base64 格式（而不上传），默认为 0
      base64LimitSize: 5 * 1024, // 5kb
      // 选择文件时的类型限制，根据实际业务改写
      allowedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      // 上传之前触发
      async onBeforeUpload(file: File) {
        try {
          return await useCompressor(file);
        } catch {
          return file;
        }
      },
      async customUpload(file: File, insertFn: InsertFnType) {
        const formData = new FormData();
        formData.append('file', file);
        const { data, error } = await uploadApi.image(formData);
        if (!error) {
          insertFn(import.meta.env.VITE_IMAGE_BASE_URL + data.fileName, '', '');
        }
      }
    }
  }
};

const handleCreated = (editor: HTMLBaseElement) => {
  editorRef.value = editor;
};

onBeforeUnmount(() => {
  editorRef.value.destroy();
  editorRef.value = null;
});
</script>

<template>
  <div class="prose">
    <div class="wang-editor">
      <Toolbar :editor="editorRef" :default-config="toolbarConfig" :mode="mode" />
      <Editor
        v-model="value"
        :default-config="editorConfig"
        :mode="mode"
        :style="`height: ${height || 400}px;`"
        @on-created="handleCreated"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.wang-editor {
  --n-border: 1px solid rgb(224, 224, 230);
  --n-border-hover: 1px solid rgb(var(--primary-color));
  --n-border-focus: 1px solid rgb(var(--primary-color));
  --n-border-radius: 3px;
  --n-box-shadow-focus: 0 0 0 2px rgb(var(--primary-color) / 0.2);
  --n-color-focus: rgba(255, 255, 255, 1);
  z-index: 9;
  max-width: 100%;
  border: var(--n-border);
  border-radius: var(--n-border-radius);
  overflow: hidden;
  transition:
    border 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
  &:hover {
    border: var(--n-border-hover);
  }
  &:focus-within {
    border: var(--n-border-focus);
    box-shadow: var(--n-box-shadow-focus);
    :deep(.w-e-text-container) {
      background: var(--n-color-focus) !important;
    }
  }
  :deep(.w-e-toolbar) {
    position: relative;
    z-index: 99;
    align-items: center;
    background: rgb(250, 250, 252);
    box-shadow: 0 0 2px 1px rgb(224, 224, 230, 0.5);
    .w-e-bar-item {
      padding: 0;
      height: 32px;
    }
    .w-e-bar-divider {
      height: 24px;
    }
    .w-e-bar-item-group {
      .w-e-bar-item-menus-container {
        margin-top: 32px;
      }
    }
    .w-e-menu-tooltip-v5 {
      &:before {
        top: 32px;
        font-size: 12px;
        border-radius: var(--n-border-radius);
      }
      &:after {
        top: 23px;
      }
    }
  }
  &.w-e-full-screen-container {
    border-radius: 0;
    border-width: 0;
    box-shadow: none;
    transition: none;
    :deep(.w-e-toolbar) {
      border-radius: 0;
      .w-e-bar-item {
        padding: 4px;
        height: 40px;
      }
    }
    :deep(.w-e-bar-item-group) {
      .w-e-bar-item-menus-container {
        margin-top: 40px;
      }
    }
    :deep(.w-e-menu-tooltip-v5) {
      &:before {
        top: 40px;
      }
      &:after {
        top: 30px;
      }
    }
  }
  :deep(.w-e-text-container) {
    line-height: 1.6;
    transition: background 0.2s ease-in-out;
    [data-slate-editor] {
      padding: 0 12px;
      p {
        margin: 6.5px 0;
      }
    }
    .w-e-text-placeholder {
      top: 6.5px;
      left: 12px;
      font-style: normal;
      color: #c2c2c2;
    }
    .w-e-scroll {
      &::-webkit-scrollbar {
        width: var(--n-scrollbar-width);
      }
      &::-webkit-scrollbar-thumb {
        background: var(--n-scrollbar-color);
        &:hover {
          background: var(--n-scrollbar-color-hover);
          cursor: pointer;
        }
      }
    }
    &:hover {
      .w-e-scroll::-webkit-scrollbar {
        opacity: 1;
      }
    }
  }
}
.dark {
  .wang-editor {
    --n-border: 1px solid #0000;
    --n-box-shadow-focus: 0 0 0 2px rgb(var(--primary-color) / 0.2);
    --n-color-focus: #424247;
    :deep(.w-e-toolbar),
    :deep(.w-e-bar) {
      filter: invert(1);
    }
    :deep(.w-e-text-container) {
      background: #424247;
      border-width: 0;
      color: #d5d5d5;
      .w-e-text-placeholder {
        color: #8a8a8a;
      }
    }
  }
}
</style>
